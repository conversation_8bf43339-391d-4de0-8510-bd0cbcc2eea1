2025-07-21 01:03:39,016 - __main__ - INFO - Starting background cleanup process: manual_cleanup_001
2025-07-21 01:03:39,153 - __main__ - INFO - Connecting to database...
2025-07-21 01:03:53,492 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 01:03:55,249 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 01:03:55,261 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:03:55,265 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:03:57,011 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:03:57,026 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:03:59,571 - __main__ - INFO - Running cleanup preview...
2025-07-21 01:03:59,603 - services.database_cleanup_service - INFO - Generating cleanup preview
2025-07-21 01:03:59,719 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:03:59,754 - httpcore.connection - DEBUG - connect_tcp.started host='api.telegram.org' port=443 local_address=None timeout=5.0 socket_options=None
2025-07-21 01:04:00,027 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029B38345310>
2025-07-21 01:04:00,028 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000029B38317250> server_hostname='api.telegram.org' timeout=5.0
2025-07-21 01:04:00,239 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029B383444D0>
2025-07-21 01:04:00,240 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:00,242 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:00,242 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:00,243 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:00,243 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:00,784 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:01 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'235'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:00,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:00,789 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:00,790 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:00,791 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:00,793 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:00,796 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'accepted_gift_types': {'unlimited_gifts': False, 'limited_gifts': False, 'unique_gifts': False, 'premium_subscription': False}, 'max_reaction_count': 11}, id=7695895150, type=<ChatType.PRIVATE>)
2025-07-21 01:04:00,797 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:00,816 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:00,820 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:00,824 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:00,825 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:00,827 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:00,827 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:01,368 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:01 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'357'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:01,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:01,372 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:01,373 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:01,374 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:01,374 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:01,376 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('MrRalte',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Love yourself', first_name='Hmangaiha', id=1484535151, last_name='MrRalte', type=<ChatType.PRIVATE>, username='MrRalte')
2025-07-21 01:04:01,377 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:01,395 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:01,398 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:01,400 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:01,401 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:01,402 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:01,402 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:01,943 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:02 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:01,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:01,947 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:01,948 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:01,948 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:01,949 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:01,950 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sahil', id=6627493810, last_name='Mishra', type=<ChatType.PRIVATE>)
2025-07-21 01:04:01,950 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:01,967 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:01,969 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:01,970 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:01,971 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:01,972 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:01,972 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:02,541 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:03 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'360'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:02,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:02,546 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:02,548 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:02,549 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:02,550 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:02,551 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('hamxa0_7',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Hamza', has_private_forwards=True, id=5056249243, last_name='Hayatt', type=<ChatType.PRIVATE>, username='hamxa0_7')
2025-07-21 01:04:02,553 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:02,570 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:02,573 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:02,574 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:02,575 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:02,576 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:02,576 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:03,117 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:03 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'263'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:03,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:03,119 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:03,120 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:03,121 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:03,122 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:03,123 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Andriniaina', id=7300193121, type=<ChatType.PRIVATE>)
2025-07-21 01:04:03,124 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:03,142 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:03,144 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:03,146 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:03,147 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:03,148 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:03,148 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:03,523 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:04 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'507'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:03,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:03,524 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:03,525 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:03,525 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:03,526 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:03,527 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='BISWAJIT', id=6477518111, last_name='Mondal', photo=ChatPhoto(big_file_id='AQADBQADncUxGyPocVQACAMAAx8VF4IBAAOuq-1OAwfo5zYE', big_file_unique_id='AQADncUxGyPocVQB', small_file_id='AQADBQADncUxGyPocVQACAIAAx8VF4IBAAOuq-1OAwfo5zYE', small_file_unique_id='AQADncUxGyPocVQAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:03,528 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:03,546 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:03,549 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:03,551 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:03,552 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:03,553 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:03,553 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:04,099 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:04 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'540'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:04,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:04,106 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:04,107 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:04,108 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:04,108 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:04,109 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Juannikk',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Juannili', id=6873375150, photo=ChatPhoto(big_file_id='AQADBQAD4MgxGyq08FYACAMAA65hr5kBAAPMii7fM3sOgzYE', big_file_unique_id='AQAD4MgxGyq08FYB', small_file_id='AQADBQAD4MgxGyq08FYACAIAA65hr5kBAAPMii7fM3sOgzYE', small_file_unique_id='AQAD4MgxGyq08FYAAQ'), type=<ChatType.PRIVATE>, username='Juannikk')
2025-07-21 01:04:04,110 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:04,128 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:04,132 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:04,134 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:04,135 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:04,136 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:04,137 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:04,678 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:04,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:04,681 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:04,683 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:04,684 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:04,685 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:04,686 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Deepu', id=7783156207, last_name='Singh', type=<ChatType.PRIVATE>)
2025-07-21 01:04:04,687 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:04,704 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:04,707 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:04,709 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:04,710 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:04,712 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:04,713 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:05,295 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'275'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:05,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:05,297 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:05,299 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:05,299 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:05,299 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:05,300 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Itz', id=7788585760, last_name='Rahim', type=<ChatType.PRIVATE>)
2025-07-21 01:04:05,300 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:05,317 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:05,318 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:05,319 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:05,320 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:05,320 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:05,320 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:05,873 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:06 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'1040'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:05,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:05,875 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:05,876 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:05,876 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:05,877 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:05,879 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('VisionHacksowner',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='𝙃𝙖𝙢 𝙋𝙖𝙞𝙨𝙖 𝙉𝙝𝙞 𝙄𝙯𝙯𝙖𝙩 𝙆𝙖𝙢𝙖𝙩𝙚 𝙃𝙖𝙞 😘', first_name='🇮🇳 𝐕𝐈𝐒𝐈𝐎𝐍 𝐎𝐖𝐍𝐄𝐑 ™', id=7653630149, photo=ChatPhoto(big_file_id='AQADBQADtL8xG2P96FUACAMAA8UgMcgBAAP5lpVlvIteDjYE', big_file_unique_id='AQADtL8xG2P96FUB', small_file_id='AQADBQADtL8xG2P96FUACAIAA8UgMcgBAAP5lpVlvIteDjYE', small_file_unique_id='AQADtL8xG2P96FUAAQ'), type=<ChatType.PRIVATE>, username='VisionHacksowner')
2025-07-21 01:04:05,881 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:05,900 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:05,902 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:05,903 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:05,903 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:05,904 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:05,905 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:06,487 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:07 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'350'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:06,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:06,489 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:06,489 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:06,490 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:06,490 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:06,491 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('ClockTGE',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Jahid', id=7521631909, last_name='Ahmed🐤Gra-Gra', type=<ChatType.PRIVATE>, username='ClockTGE')
2025-07-21 01:04:06,491 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:06,509 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:06,512 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:06,514 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:06,514 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:06,516 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:06,516 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:07,066 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:07 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'331'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:07,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:07,067 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:07,067 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:07,068 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:07,068 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:07,068 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('GREEDY012',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='GREEDY BOY 😤', id=6592380228, type=<ChatType.PRIVATE>, username='GREEDY012')
2025-07-21 01:04:07,068 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:07,086 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:07,087 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:07,088 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:07,088 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:07,089 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:07,089 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:07,632 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:08 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'280'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:07,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:07,634 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:07,634 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:07,635 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:07,635 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:07,636 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Santosh', id=6698811808, last_name='yadhav', type=<ChatType.PRIVATE>)
2025-07-21 01:04:07,636 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:07,653 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:07,654 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:07,656 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:07,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:07,657 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:07,657 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:08,226 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:08 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'275'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:08,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:08,228 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:08,228 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:08,229 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:08,229 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:08,230 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Yash', id=5613288050, last_name='Yash', type=<ChatType.PRIVATE>)
2025-07-21 01:04:08,230 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:08,247 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:08,249 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:08,250 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:08,250 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:08,250 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:08,251 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:08,656 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:09 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'504'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:08,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:08,658 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:08,659 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:08,659 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:08,660 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:08,661 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Shanert', id=7323314365, last_name='Himm', photo=ChatPhoto(big_file_id='AQADBAADRsYxG9yW0FMACAMAA73ogLQBAAPTUqyElAunfzYE', big_file_unique_id='AQADRsYxG9yW0FMB', small_file_id='AQADBAADRsYxG9yW0FMACAIAA73ogLQBAAPTUqyElAunfzYE', small_file_unique_id='AQADRsYxG9yW0FMAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:08,661 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:08,679 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:08,682 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:08,683 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:08,684 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:08,685 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:08,685 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:09,281 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:09 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'543'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:09,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:09,283 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:09,283 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:09,284 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:09,284 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:09,285 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Zarif3839',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Art titas', id=7149512402, photo=ChatPhoto(big_file_id='AQADBQADB8MxG_0OOFYACAMAA9LmJKoBAAOjai45LxCxXDYE', big_file_unique_id='AQADB8MxG_0OOFYB', small_file_id='AQADBQADB8MxG_0OOFYACAIAA9LmJKoBAAOjai45LxCxXDYE', small_file_unique_id='AQADB8MxG_0OOFYAAQ'), type=<ChatType.PRIVATE>, username='Zarif3839')
2025-07-21 01:04:09,285 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:09,303 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:09,304 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:09,305 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:09,306 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:09,306 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:09,307 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:10,025 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:10 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'1847'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:10,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:10,026 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:10,028 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:10,028 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:10,029 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:10,029 - telegram.Bot - DEBUG - Chat(accent_color_id=7, api_kwargs={'can_send_gift': True, 'business_intro': {'title': 'HLO DOREMON', 'sticker': {'width': 512, 'height': 512, 'emoji': '🙂', 'set_name': 'spiderfiles_by_fStikBot', 'is_animated': False, 'is_video': False, 'type': 'regular', 'thumbnail': {'file_id': 'AAMCBQADFQABaH1EshZFDlldNCoCGBqmk0Af-EUAAo0WAAIpZ3FXFbImi_9tVIgBAAdtAAM2BA', 'file_unique_id': 'AQADjRYAAilncVdy', 'file_size': 14122, 'width': 320, 'height': 320}, 'thumb': {'file_id': 'AAMCBQADFQABaH1EshZFDlldNCoCGBqmk0Af-EUAAo0WAAIpZ3FXFbImi_9tVIgBAAdtAAM2BA', 'file_unique_id': 'AQADjRYAAilncVdy', 'file_size': 14122, 'width': 320, 'height': 320}, 'file_id': 'CAACAgUAAxUAAWh9RLIWRQ5ZXTQqAhgappNAH_hFAAKNFgACKWdxVxWyJov_bVSINgQ', 'file_unique_id': 'AgADjRYAAilncVc', 'file_size': 25000}}, 'business_location': {'location': {'latitude': 28.629631, 'longitude': 77.219857}, 'address': '\u3000. \u3000\u2008˚\u3000.\u3000\u3000\u3000\u3000\u3000 . ✦˚\u3000.\u3000\u3000\u3000\u3000'}, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002713234693, 'title': 'WHATSAPP FILES', 'username': 'doremonproofs18', 'type': 'channel'}, 'max_reaction_count': 11}, bio='जय श्री राम ABOUTUS :- @ABOUTUSDOREMON PROOF :- @doremonproofs18 DIRECT PAY:- @DIRECTPAYUPI', emoji_status_custom_emoji_id='5334998226636390258', first_name='ʷˢᵃᵍᵉⁿᵗ | 𝗚𝗜𝗔𝗡', id=5667044343, photo=ChatPhoto(big_file_id='AQADBQADmccxGz-C6VcACAMAA_c7yFEBAAO3h4_KAjmKvjYE', big_file_unique_id='AQADmccxGz-C6VcB', small_file_id='AQADBQADmccxGz-C6VcACAIAA_c7yFEBAAO3h4_KAjmKvjYE', small_file_unique_id='AQADmccxGz-C6VcAAQ'), profile_accent_color_id=13, profile_background_custom_emoji_id='5357199488115030155', type=<ChatType.PRIVATE>)
2025-07-21 01:04:10,033 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:10,051 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:10,053 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:10,054 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:10,054 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:10,054 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:10,054 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:10,605 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:11 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'325'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:10,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:10,606 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:10,606 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:10,606 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:10,606 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:10,607 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Manishmishra73',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Megnath', id=5089729082, type=<ChatType.PRIVATE>, username='Manishmishra73')
2025-07-21 01:04:10,608 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:10,626 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:10,627 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:10,629 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:10,629 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:10,630 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:10,631 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:11,171 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:11 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'257'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:11,172 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:11,172 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:11,173 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:11,173 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:11,173 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:11,174 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Joban', id=6811470883, type=<ChatType.PRIVATE>)
2025-07-21 01:04:11,174 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:11,192 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:11,194 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:11,196 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:11,197 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:11,198 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:11,199 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:11,743 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:12 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'287'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:11,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:11,744 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:11,746 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:11,746 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:11,746 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:11,748 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Krishna', has_private_forwards=True, id=5452616660, type=<ChatType.PRIVATE>)
2025-07-21 01:04:11,748 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:11,766 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:11,768 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:11,768 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:11,768 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:11,769 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:11,770 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:12,315 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:12 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'546'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:12,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:12,317 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:12,317 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:12,317 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:12,317 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:12,318 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('NOBITA_MR',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='D Kripal Reddy', id=1807838614, photo=ChatPhoto(big_file_id='AQADBQADGqwxG8eoUVYACAMAA5ZtwWsABJhq6Nw8A3GjNgQ', big_file_unique_id='AQADGqwxG8eoUVYB', small_file_id='AQADBQADGqwxG8eoUVYACAIAA5ZtwWsABJhq6Nw8A3GjNgQ', small_file_unique_id='AQADGqwxG8eoUVYAAQ'), type=<ChatType.PRIVATE>, username='NOBITA_MR')
2025-07-21 01:04:12,318 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:12,336 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:12,337 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:12,337 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:12,338 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:12,339 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:12,339 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:12,765 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:13 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'337'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:12,765 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:12,767 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:12,767 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:12,767 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:12,767 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:12,768 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Amesypop',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Nevergive up', first_name='RøarX', id=5797927220, type=<ChatType.PRIVATE>, username='Amesypop')
2025-07-21 01:04:12,768 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:12,786 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:12,787 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:12,789 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:12,789 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:12,789 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:12,789 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:13,177 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:13 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'265'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:13,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:13,178 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:13,180 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:13,180 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:13,181 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:13,181 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Krisnanda ap.', id=7303761632, type=<ChatType.PRIVATE>)
2025-07-21 01:04:13,182 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:13,200 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:13,203 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:13,204 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:13,204 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:13,205 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:13,207 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:13,571 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:14 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'322'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:13,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:13,573 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:13,574 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:13,574 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:13,574 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:13,576 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Hjjjhbbjm',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ruan Barbosa F', id=7660216543, type=<ChatType.PRIVATE>, username='Hjjjhbbjm')
2025-07-21 01:04:13,578 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:13,595 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:13,597 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:13,598 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:13,598 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:13,601 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:13,601 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:14,146 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:14 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'284'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:14,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:14,147 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:14,147 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:14,149 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:14,149 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:14,149 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Fedra', id=8112359448, last_name='FIFALIANTSOA', type=<ChatType.PRIVATE>)
2025-07-21 01:04:14,149 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:14,166 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:14,168 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:14,168 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:14,168 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:14,170 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:14,170 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:14,715 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:15 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'579'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:14,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:14,718 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:14,719 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:14,720 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:14,720 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:14,721 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Krishnaambhure',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Krishna', id=5959963072, last_name='Ambhure Patil', photo=ChatPhoto(big_file_id='AQADBQADEbcxG8DeeFQACAMAA8DRPWMBAAMEmP57e28h1DYE', big_file_unique_id='AQADEbcxG8DeeFQB', small_file_id='AQADBQADEbcxG8DeeFQACAIAA8DRPWMBAAMEmP57e28h1DYE', small_file_unique_id='AQADEbcxG8DeeFQAAQ'), type=<ChatType.PRIVATE>, username='Krishnaambhure')
2025-07-21 01:04:14,722 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:14,739 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:14,741 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:14,743 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:14,743 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:14,744 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:14,744 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:15,276 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:15 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'273'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:15,277 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:15,278 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:15,279 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:15,280 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:15,280 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:15,281 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Bhai', id=5459779296, last_name='Ji', type=<ChatType.PRIVATE>)
2025-07-21 01:04:15,281 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:15,299 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:15,301 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:15,302 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:15,302 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:15,304 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:15,304 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:15,676 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:16 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'272'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:15,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:15,678 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:15,679 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:15,679 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:15,680 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:15,680 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Hm', id=8108868628, last_name='Hmm', type=<ChatType.PRIVATE>)
2025-07-21 01:04:15,681 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:15,698 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:15,700 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:15,700 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:15,701 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:15,701 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:15,701 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:16,236 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:16 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'495'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:16,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:16,238 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:16,238 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:16,239 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:16,239 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:16,240 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='v', id=7318477199, last_name='d', photo=ChatPhoto(big_file_id='AQADBQADZb4xG-W4mVcACAMAA48ZN7QBAAM6FlWRHONbtTYE', big_file_unique_id='AQADZb4xG-W4mVcB', small_file_id='AQADBQADZb4xG-W4mVcACAIAA48ZN7QBAAM6FlWRHONbtTYE', small_file_unique_id='AQADZb4xG-W4mVcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:16,240 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:16,259 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:16,262 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:16,264 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:16,264 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:16,265 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:16,266 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:16,642 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:17 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'308'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:16,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:16,646 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:16,646 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:16,646 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:16,646 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:16,648 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Side899',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='SIDE', id=7272552264, type=<ChatType.PRIVATE>, username='Side899')
2025-07-21 01:04:16,648 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:16,666 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:16,666 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:16,668 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:16,668 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:16,668 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:16,668 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:17,208 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:17 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'341'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:17,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:17,209 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:17,209 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:17,211 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:17,211 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:17,213 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('katoch002',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Abhay', has_private_forwards=True, id=7353876369, type=<ChatType.PRIVATE>, username='katoch002')
2025-07-21 01:04:17,213 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:17,231 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:17,232 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:17,234 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:17,234 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:17,235 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:17,235 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:17,778 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:18 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:17,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:17,781 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:17,782 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:17,782 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:17,782 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:17,783 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Vidhi', id=7209815373, last_name='Dedhia', type=<ChatType.PRIVATE>)
2025-07-21 01:04:17,783 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:17,802 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:17,804 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:17,805 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:17,805 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:17,805 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:17,807 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:18,186 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:18 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'560'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:18,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:18,188 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:18,188 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:18,189 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:18,189 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:18,189 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('morteza649',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='💗💜.¸¸.•´¯ºஇ•´♥️ •●♥️༺amer༻♥️●• ♥️•´இº.¸¸.•´¯💜💗', id=7888307971, type=<ChatType.PRIVATE>, username='morteza649')
2025-07-21 01:04:18,190 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:18,208 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:18,211 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:18,213 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:18,214 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:18,215 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:18,215 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:18,760 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:19 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'346'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:18,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:18,762 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:18,762 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:18,763 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:18,764 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:18,765 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Booby1150',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Noub', id=6985157516, last_name='Shutss 🐾', type=<ChatType.PRIVATE>, username='Booby1150')
2025-07-21 01:04:18,766 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:18,783 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:18,788 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:18,790 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:18,791 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:18,794 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:18,794 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:19,352 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:19 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'580'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:19,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:19,355 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:19,356 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:19,357 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:19,358 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:19,358 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Beast_kaii',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='I am best', first_name='Pratap', id=5054020799, last_name='Gupta', photo=ChatPhoto(big_file_id='AQADBQADLLoxG2mQ0VUACAMAA788Pi0BAAPVSCJN0KemozYE', big_file_unique_id='AQADLLoxG2mQ0VUB', small_file_id='AQADBQADLLoxG2mQ0VUACAIAA788Pi0BAAPVSCJN0KemozYE', small_file_unique_id='AQADLLoxG2mQ0VUAAQ'), type=<ChatType.PRIVATE>, username='Beast_kaii')
2025-07-21 01:04:19,360 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:19,377 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:19,380 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:19,382 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:19,382 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:19,384 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:19,385 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:20,018 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:20 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'296'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:20,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:20,021 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:20,022 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:20,022 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:20,022 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:20,023 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='<EMAIL>', id=7951848389, last_name='Mahaveer', type=<ChatType.PRIVATE>)
2025-07-21 01:04:20,023 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:20,040 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:20,043 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:20,046 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:20,046 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:20,049 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:20,049 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:20,518 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:21 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'337'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:20,519 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:20,520 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:20,521 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:20,521 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:20,521 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:20,522 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Gaming84649',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='freeofficial 🐐', id=6609279672, type=<ChatType.PRIVATE>, username='Gaming84649')
2025-07-21 01:04:20,522 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:20,540 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:20,542 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:20,543 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:20,544 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:20,545 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:20,546 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:21,095 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:21 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'280'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:21,098 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:21,099 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:21,099 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:21,099 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:21,100 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:21,101 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Mami zote', id=7687921311, last_name='zote', type=<ChatType.PRIVATE>)
2025-07-21 01:04:21,102 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:21,120 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:21,122 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:21,123 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:21,124 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:21,124 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:21,126 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:21,671 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:22 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'353'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:21,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:21,674 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:21,674 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:21,675 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:21,675 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:21,677 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('SamareshBairagee',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Samaresh', id=7642531187, last_name='Bairagee', type=<ChatType.PRIVATE>, username='SamareshBairagee')
2025-07-21 01:04:21,678 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:21,696 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:21,698 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:21,701 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:21,701 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:21,702 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:21,703 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:22,244 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:22 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'258'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:22,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:22,245 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:22,246 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:22,246 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:22,247 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:22,248 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Piyush', id=8097786938, type=<ChatType.PRIVATE>)
2025-07-21 01:04:22,249 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:22,266 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:22,269 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:22,271 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:22,272 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:22,273 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:22,273 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:22,848 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:23 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'272'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:22,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:22,850 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:22,851 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:22,851 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:22,852 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:22,853 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Chi', id=8097232620, last_name='Dy', type=<ChatType.PRIVATE>)
2025-07-21 01:04:22,853 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:22,871 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:22,873 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:22,874 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:22,875 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:22,876 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:22,876 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:23,418 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:23 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'553'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:23,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:23,420 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:23,420 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:23,420 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:23,421 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:23,422 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Ibrahimkhan_6_5',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='IBRAHIM', id=7702233894, photo=ChatPhoto(big_file_id='AQADBQADS8oxG4eiuVQACAMAAybDFssBAAOH6TUD-FDZJzYE', big_file_unique_id='AQADS8oxG4eiuVQB', small_file_id='AQADBQADS8oxG4eiuVQACAIAAybDFssBAAOH6TUD-FDZJzYE', small_file_unique_id='AQADS8oxG4eiuVQAAQ'), type=<ChatType.PRIVATE>, username='Ibrahimkhan_6_5')
2025-07-21 01:04:23,422 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:23,440 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:23,442 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:23,444 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:23,445 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:23,446 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:23,447 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:23,994 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:24 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'556'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:23,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:23,996 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:23,996 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:23,997 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:23,997 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:23,998 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Raven_366',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Raven', id=5724211642, last_name='36', photo=ChatPhoto(big_file_id='AQADBQAD1LMxGx3oMFQACAMAA7qJMFUBAANk2JR9q7BNRTYE', big_file_unique_id='AQAD1LMxGx3oMFQB', small_file_id='AQADBQAD1LMxGx3oMFQACAIAA7qJMFUBAANk2JR9q7BNRTYE', small_file_unique_id='AQAD1LMxGx3oMFQAAQ'), type=<ChatType.PRIVATE>, username='Raven_366')
2025-07-21 01:04:23,999 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:24,017 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:24,019 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:24,020 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:24,020 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:24,021 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:24,021 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:24,582 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:25 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:24,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:24,585 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:24,586 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:24,586 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:24,587 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:24,588 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sankar', id=7842613984, last_name='Buri', type=<ChatType.PRIVATE>)
2025-07-21 01:04:24,590 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:24,608 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:24,612 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:24,616 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:24,616 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:24,617 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:24,618 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:25,189 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:25 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:25,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:25,198 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:25,198 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:25,199 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:25,199 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:25,200 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rocky', id=7466288243, last_name='Pandey', type=<ChatType.PRIVATE>)
2025-07-21 01:04:25,201 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:25,219 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:25,222 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:25,223 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:25,224 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:25,225 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:25,225 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:25,776 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:26 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'678'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:25,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:25,778 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:25,779 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:25,779 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:25,779 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:25,780 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('sagor_babu_7',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Poor man', first_name='𝙎𝘼𝙂𝙊𝙍', id=1987762161, last_name='𝘽𝘼𝘽𝙐', photo=ChatPhoto(big_file_id='AQADBQADH8cxG644OFUACAMAA_HXenYABIg0BHxo4WuFNgQ', big_file_unique_id='AQADH8cxG644OFUB', small_file_id='AQADBQADH8cxG644OFUACAIAA_HXenYABIg0BHxo4WuFNgQ', small_file_unique_id='AQADH8cxG644OFUAAQ'), type=<ChatType.PRIVATE>, username='sagor_babu_7')
2025-07-21 01:04:25,780 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:25,798 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:25,800 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:25,802 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:25,802 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:25,804 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:25,804 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:26,179 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:26 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'343'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:26,182 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:26,183 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:26,183 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:26,183 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:26,184 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:26,184 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('botManishek',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Bot', has_private_forwards=True, id=6965576802, type=<ChatType.PRIVATE>, username='botManishek')
2025-07-21 01:04:26,184 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:26,202 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:26,203 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:26,204 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:26,204 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:26,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:26,206 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:26,753 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:27 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'600'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:26,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:26,756 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:26,756 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:26,757 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:26,757 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:26,759 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('see001j',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='i intrest to watch science fiction movies.', first_name='spj', id=5452342670, last_name='P', photo=ChatPhoto(big_file_id='AQADBQAD8bwxG7V9wFYACAMAA44l_EQBAANPy2o6zwYhvjYE', big_file_unique_id='AQAD8bwxG7V9wFYB', small_file_id='AQADBQAD8bwxG7V9wFYACAIAA44l_EQBAANPy2o6zwYhvjYE', small_file_unique_id='AQAD8bwxG7V9wFYAAQ'), type=<ChatType.PRIVATE>, username='see001j')
2025-07-21 01:04:26,759 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:26,778 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:26,780 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:26,781 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:26,782 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:26,782 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:26,782 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:27,336 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:27 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'311'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:27,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:27,340 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:27,341 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:27,341 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:27,341 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:27,342 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Alibroali',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ali', id=7221759636, type=<ChatType.PRIVATE>, username='Alibroali')
2025-07-21 01:04:27,342 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:27,346 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:27,349 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:27,350 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:27,350 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:27,352 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:27,352 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:27,901 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:28 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'492'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:27,903 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:27,903 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:27,904 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:27,905 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:27,905 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:27,906 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kaliash Mohare', id=7329149474, photo=ChatPhoto(big_file_id='AQADBQAD0boxG2F20FUACAMAAyLy2bQBAAMbSX80MhtTWjYE', big_file_unique_id='AQAD0boxG2F20FUB', small_file_id='AQADBQAD0boxG2F20FUACAIAAyLy2bQBAAMbSX80MhtTWjYE', small_file_unique_id='AQAD0boxG2F20FUAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:27,907 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:27,925 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:27,927 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:27,928 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:27,928 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:27,930 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:27,930 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:28,477 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:29 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'281'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:28,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:28,480 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:28,480 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:28,482 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:28,482 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:28,483 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='kanchan', id=5820046848, last_name='Ramawat', type=<ChatType.PRIVATE>)
2025-07-21 01:04:28,483 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:28,502 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:28,503 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:28,505 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:28,505 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:28,507 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:28,507 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:28,890 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:29 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'604'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:28,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:28,894 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:28,894 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:28,895 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:28,895 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:28,896 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Plutominal',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='LOADING...', first_name='Aus', has_private_forwards=True, id=6306942065, last_name='Tin', photo=ChatPhoto(big_file_id='AQADBAADJcgxG6XMAVEACAMAA3FM7HcBAAP6jM1Ps_MmXzYE', big_file_unique_id='AQADJcgxG6XMAVEB', small_file_id='AQADBAADJcgxG6XMAVEACAIAA3FM7HcBAAP6jM1Ps_MmXzYE', small_file_unique_id='AQADJcgxG6XMAVEAAQ'), type=<ChatType.PRIVATE>, username='Plutominal')
2025-07-21 01:04:28,897 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:28,915 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:28,916 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:28,917 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:28,917 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:28,918 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:28,918 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:29,301 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:29 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'501'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:29,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:29,302 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:29,303 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:29,303 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:29,303 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:29,303 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Mohan', id=6041839958, last_name='Rao', photo=ChatPhoto(big_file_id='AQADBQADdMExG-zuIVYACAMAA1YpH2gBAAMbMyJTR6p5RzYE', big_file_unique_id='AQADdMExG-zuIVYB', small_file_id='AQADBQADdMExG-zuIVYACAIAA1YpH2gBAAMbMyJTR6p5RzYE', small_file_unique_id='AQADdMExG-zuIVYAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:29,303 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:29,321 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:29,322 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:29,322 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:29,322 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:29,322 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:29,322 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:29,867 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:30 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'344'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:29,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:29,869 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:29,869 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:29,870 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:29,870 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:29,870 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Lilyllilly',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Teuzin', has_private_forwards=True, id=7627710896, type=<ChatType.PRIVATE>, username='Lilyllilly')
2025-07-21 01:04:29,870 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:29,888 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:29,890 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:29,890 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:29,890 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:29,891 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:29,892 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:30,260 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:30 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'338'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:30,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:30,263 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:30,263 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:30,263 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:30,263 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:30,263 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('noTcarDer999',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='NOT', id=6895708814, last_name='CARDER', type=<ChatType.PRIVATE>, username='noTcarDer999')
2025-07-21 01:04:30,263 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:30,281 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:30,282 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:30,283 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:30,283 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:30,284 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:30,284 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:30,665 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:31 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'522'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:30,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:30,666 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:30,666 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:30,666 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:30,666 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:30,667 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='🙏🏼', first_name='Suraj singh', id=6065298112, photo=ChatPhoto(big_file_id='AQADBQADB8IxGxwmcFYACAMAA8AahWkBAAO24jc4bXu1lDYE', big_file_unique_id='AQADB8IxGxwmcFYB', small_file_id='AQADBQADB8IxGxwmcFYACAIAA8AahWkBAAO24jc4bXu1lDYE', small_file_unique_id='AQADB8IxGxwmcFYAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:30,667 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:30,685 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:30,686 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:30,687 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:30,687 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:30,688 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:30,688 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:31,238 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:31 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'620'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:31,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:31,240 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:31,240 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:31,241 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:31,241 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:31,242 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('Useryash12three',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='PRIVATE PAID H4X JOIN KRO FREE HACK DUNGA', first_name='Rounak', id=6782581917, last_name='Pal', photo=ChatPhoto(big_file_id='AQADBQADjMAxGxuNOFYACAMAA538RZQBAAMtDyH0lMRkmjYE', big_file_unique_id='AQADjMAxGxuNOFYB', small_file_id='AQADBQADjMAxGxuNOFYACAIAA538RZQBAAMtDyH0lMRkmjYE', small_file_unique_id='AQADjMAxGxuNOFYAAQ'), type=<ChatType.PRIVATE>, username='Useryash12three')
2025-07-21 01:04:31,243 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:31,260 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:31,262 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:31,264 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:31,264 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:31,264 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:31,265 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:31,797 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:32 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'283'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:31,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:31,798 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:31,798 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:31,799 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:31,799 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:31,799 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rohit Kumar', id=6781136923, last_name='Kewat', type=<ChatType.PRIVATE>)
2025-07-21 01:04:31,799 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:31,817 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:31,818 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:31,818 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:31,819 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:31,819 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:31,819 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:32,360 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:32 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'275'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:32,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:32,363 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:32,363 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:32,363 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:32,363 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:32,364 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Muni', id=6693528540, last_name='Raju', type=<ChatType.PRIVATE>)
2025-07-21 01:04:32,364 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:32,382 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:32,383 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:32,384 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:32,384 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:32,385 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:32,385 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:32,924 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:33 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'282'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:32,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:32,925 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:32,927 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:32,927 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:32,927 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:32,929 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='PRASHANT', id=6270603994, last_name='PRADHAN', type=<ChatType.PRIVATE>)
2025-07-21 01:04:32,929 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:32,946 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:32,947 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:32,947 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:32,947 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:32,949 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:32,949 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:33,486 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:34 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'336'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:33,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:33,488 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:33,488 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:33,488 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:33,489 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:33,489 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Kundan_yad',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kundan', id=8143124796, last_name='Yadav', type=<ChatType.PRIVATE>, username='Kundan_yad')
2025-07-21 01:04:33,489 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:33,509 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:33,510 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:33,512 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:33,512 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:33,514 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:33,514 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:34,054 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:34 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'275'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:34,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:34,055 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:34,056 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:34,056 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:34,056 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:34,058 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Abhi', id=6313312921, last_name='Abhi', type=<ChatType.PRIVATE>)
2025-07-21 01:04:34,058 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:34,076 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:34,078 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:34,080 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:34,080 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:34,080 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:34,080 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:34,459 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:35 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'340'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:34,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:34,461 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:34,461 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:34,462 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:34,462 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:34,462 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Skippa2AFlippa',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ami', id=7862642502, last_name='Laye', type=<ChatType.PRIVATE>, username='Skippa2AFlippa')
2025-07-21 01:04:34,462 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:34,481 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:34,482 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:34,484 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:34,484 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:34,485 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:34,485 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,023 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:35 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'385'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:35,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:35,025 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,025 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:35,025 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:35,026 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:35,026 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('SP_Sakib',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='🤫💸💸💸', first_name='SP', id=7008088643, last_name='Sakib', type=<ChatType.PRIVATE>, username='SP_Sakib')
2025-07-21 01:04:35,026 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:35,044 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:35,046 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,046 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:35,046 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,047 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:35,047 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,421 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:36 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'464'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:35,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:35,423 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,424 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:35,424 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:35,425 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:35,425 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Aliva_Allah',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Nothing is impossible, the word itself says “I’m possible"! 💪🏻💪🏻💪', first_name='Alvia ☬', id=6918296285, type=<ChatType.PRIVATE>, username='Aliva_Allah')
2025-07-21 01:04:35,426 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:35,444 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:35,445 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,447 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:35,447 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,447 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:35,447 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,830 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:36 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'556'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:35,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:35,832 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,832 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:35,832 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:35,832 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:35,833 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('ajitgupt',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ajit', id=5732518221, last_name='Gupta', photo=ChatPhoto(big_file_id='AQADBQADncUxGwEoyFQACAMAA01Jr1UBAAPlXhYEcSxj3zYE', big_file_unique_id='AQADncUxGwEoyFQB', small_file_id='AQADBQADncUxGwEoyFQACAIAA01Jr1UBAAPlXhYEcSxj3zYE', small_file_unique_id='AQADncUxGwEoyFQAAQ'), type=<ChatType.PRIVATE>, username='ajitgupt')
2025-07-21 01:04:35,833 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:35,852 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:35,854 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:35,854 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:35,854 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:35,855 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:35,855 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:36,405 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:36 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'546'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:36,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:36,411 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:36,412 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:36,412 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:36,414 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:36,415 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='🍁🍁❤️', first_name='Error', id=7841479393, last_name='406', photo=ChatPhoto(big_file_id='AQADBQADZsoxG9GpwVUACAMAA-F6Y9MBAANjHtkTbg5HHjYE', big_file_unique_id='AQADZsoxG9GpwVUB', small_file_id='AQADBQADZsoxG9GpwVUACAIAA-F6Y9MBAANjHtkTbg5HHjYE', small_file_unique_id='AQADZsoxG9GpwVUAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:36,416 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:36,434 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:36,436 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:36,438 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:36,438 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:36,440 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:36,441 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:36,997 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:37 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'284'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:36,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:36,999 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:36,999 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:36,999 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:37,000 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:37,001 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Anji Naidu', id=7968683959, last_name='Puvvala', type=<ChatType.PRIVATE>)
2025-07-21 01:04:37,001 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:37,018 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:37,020 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:37,021 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:37,021 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:37,021 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:37,021 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:37,584 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:38 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:37,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:37,585 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:37,586 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:37,586 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:37,587 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:37,588 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rusiya', id=6728515735, last_name='Begum', type=<ChatType.PRIVATE>)
2025-07-21 01:04:37,588 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:37,606 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:37,608 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:37,610 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:37,610 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:37,611 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:37,611 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:38,018 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:38 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'313'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:38,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:38,019 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:38,020 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:38,020 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:38,021 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:38,022 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Kailaksi',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kailash', id=7917827827, type=<ChatType.PRIVATE>, username='Kailaksi')
2025-07-21 01:04:38,022 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:38,040 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:38,042 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:38,043 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:38,043 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:38,044 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:38,044 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:38,427 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:39 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'444'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:38,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:38,429 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:38,430 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:38,430 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:38,430 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:38,430 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Anwar786786786',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='"WhatsApp File Work 📂 | Legal & High Paying 💸 | DM to Start ✅"', first_name='Anwar', id=6030016745, last_name='Shaikh', type=<ChatType.PRIVATE>, username='Anwar786786786')
2025-07-21 01:04:38,431 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:38,448 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:38,448 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:38,450 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:38,450 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:38,450 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:38,452 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:38,991 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:39 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'316'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:38,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:38,993 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:38,993 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:38,993 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:38,993 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:38,995 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Jazielllll',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Jaziel', id=8155843246, type=<ChatType.PRIVATE>, username='Jazielllll')
2025-07-21 01:04:38,995 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:39,013 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:39,014 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:39,015 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:39,015 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:39,015 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:39,015 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:39,572 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:40 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'299'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:39,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:39,575 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:39,576 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:39,577 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:39,578 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:39,579 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='ایرانسل 5905', id=7476129788, type=<ChatType.PRIVATE>)
2025-07-21 01:04:39,579 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:39,596 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:39,599 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:39,600 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:39,600 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:39,602 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:39,602 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:39,977 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:40 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:39,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:39,979 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:39,979 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:39,980 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:39,980 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:39,981 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Parmar', id=7990589735, last_name='Kalu', type=<ChatType.PRIVATE>)
2025-07-21 01:04:39,981 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:40,000 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:40,000 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:40,002 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:40,002 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:40,002 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:40,002 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:40,557 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:41 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'505'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:40,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:40,558 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:40,559 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:40,559 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:40,560 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:40,560 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='itx Shoeb', id=7486216505, last_name='Roy', photo=ChatPhoto(big_file_id='AQADBQADx8ExG19_MFcACAMAAzmZNr4BAANuiBvrvuCAYDYE', big_file_unique_id='AQADx8ExG19_MFcB', small_file_id='AQADBQADx8ExG19_MFcACAIAAzmZNr4BAANuiBvrvuCAYDYE', small_file_unique_id='AQADx8ExG19_MFcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:40,560 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:40,577 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:40,577 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:40,580 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:40,580 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:40,580 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:40,580 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:41,121 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:41 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'334'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:41,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:41,123 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:41,123 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:41,123 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:41,123 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:41,123 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('prem_reddy',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Prem', id=6809233530, last_name='Reddy', type=<ChatType.PRIVATE>, username='prem_reddy')
2025-07-21 01:04:41,123 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:41,142 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:41,142 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:41,144 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:41,144 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:41,144 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:41,144 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:41,710 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'411'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:41,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:41,711 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:41,711 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:41,711 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:41,711 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:41,711 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Meerame1',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Without sacrifice,dreams remain incomplete', first_name='Meera', has_private_forwards=True, id=5172939448, last_name='Kumari', type=<ChatType.PRIVATE>, username='Meerame1')
2025-07-21 01:04:41,711 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:41,728 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:41,730 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:41,730 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:41,730 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:41,730 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:41,731 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:42,107 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'335'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:42,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:42,110 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:42,110 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:42,111 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:42,111 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:42,111 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Mohit87653',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Mohit', id=7584418137, last_name='Singh', type=<ChatType.PRIVATE>, username='Mohit87653')
2025-07-21 01:04:42,111 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:42,129 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:42,130 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:42,131 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:42,131 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:42,132 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:42,132 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:42,681 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:43 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'334'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:42,681 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:42,681 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:42,683 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:42,683 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:42,683 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:42,683 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Jagkum',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Thamarai', id=5396112261, last_name='Kumaresan', type=<ChatType.PRIVATE>, username='Jagkum')
2025-07-21 01:04:42,683 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:42,702 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:42,703 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:42,704 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:42,704 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:42,705 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:42,705 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:43,254 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:43 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'335'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:43,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:43,256 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:43,257 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:43,257 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:43,257 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:43,258 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('rukminir066',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rukmini', id=7604097170, last_name='R', type=<ChatType.PRIVATE>, username='rukminir066')
2025-07-21 01:04:43,258 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:43,277 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:43,278 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:43,281 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:43,281 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:43,282 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:43,282 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:43,824 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:44 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'270'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:43,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:43,826 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:43,826 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:43,826 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:43,827 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:43,827 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Yours 🎶', id=1033122196, type=<ChatType.PRIVATE>)
2025-07-21 01:04:43,827 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:43,844 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:43,845 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:43,846 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:43,846 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:43,846 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:43,846 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:44,483 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:45 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'772'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:44,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:44,484 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:44,485 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:44,485 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:44,485 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:44,485 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Amonspecialist',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002854426894, 'title': 'Redirect', 'username': 'konguro', 'type': 'channel'}, 'max_reaction_count': 11}, bio='@GlobelXl | @GlobelXlEscrow | @Globelvouch | ig : @amonspecialist', first_name='Amon', has_private_forwards=True, id=5455728527, last_name='💼', photo=ChatPhoto(big_file_id='AQADBQAD5cwxGyE_SVcACAMAA4_PL0UBAANl1YhwxSWVAjYE', big_file_unique_id='AQAD5cwxGyE_SVcB', small_file_id='AQADBQAD5cwxGyE_SVcACAIAA4_PL0UBAANl1YhwxSWVAjYE', small_file_unique_id='AQAD5cwxGyE_SVcAAQ'), type=<ChatType.PRIVATE>, username='Amonspecialist')
2025-07-21 01:04:44,486 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:44,504 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:44,505 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:44,506 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:44,506 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:44,507 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:44,508 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:44,890 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:45 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'276'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:44,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:44,892 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:44,893 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:44,893 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:44,893 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:44,894 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sela', id=7666248094, last_name='Sella', type=<ChatType.PRIVATE>)
2025-07-21 01:04:44,894 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:44,913 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:44,915 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:44,917 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:44,918 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:44,919 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:44,919 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:45,470 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'542'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:45,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:45,472 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:45,472 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:45,472 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:45,473 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:45,473 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('bkhhhhhhh',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='___6z___', id=6479071660, photo=ChatPhoto(big_file_id='AQADBQADRrwxG2fHYVYACAMAA6zJLoIBAAN0SddT7eGCcTYE', big_file_unique_id='AQADRrwxG2fHYVYB', small_file_id='AQADBQADRrwxG2fHYVYACAIAA6zJLoIBAAN0SddT7eGCcTYE', small_file_unique_id='AQADRrwxG2fHYVYAAQ'), type=<ChatType.PRIVATE>, username='bkhhhhhhh')
2025-07-21 01:04:45,474 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:45,491 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:45,493 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:45,494 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:45,494 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:45,495 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:45,495 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:45,874 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'322'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:45,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:45,876 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:45,876 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:45,877 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:45,877 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:45,877 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('HHHkn13',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='H', id=7527925764, last_name='Kn', type=<ChatType.PRIVATE>, username='HHHkn13')
2025-07-21 01:04:45,877 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:45,895 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:45,896 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:45,896 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:45,896 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:45,896 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:45,896 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:46,445 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:47 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'489'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:46,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:46,445 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:46,445 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:46,445 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:46,445 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:46,447 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Piyush Mega', id=6204582392, photo=ChatPhoto(big_file_id='AQADBQADG8YxGymIkFcACAMAA_hp0nEBAAOhO70HCAsshTYE', big_file_unique_id='AQADG8YxGymIkFcB', small_file_id='AQADBQADG8YxGymIkFcACAIAA_hp0nEBAAOhO70HCAsshTYE', small_file_unique_id='AQADG8YxGymIkFcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:46,447 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:46,465 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:46,465 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:46,465 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:46,467 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:46,467 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:46,467 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:47,024 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:47 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'529'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:47,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:47,025 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:47,025 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:47,025 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:47,025 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:47,025 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Call_me_v3__king', first_name='Shaik', id=6369976018, last_name='Zaheer', photo=ChatPhoto(big_file_id='AQADBQADj84xG6UHQVcACAMAA9IernsBAAMTpqHWctxq0zYE', big_file_unique_id='AQADj84xG6UHQVcB', small_file_id='AQADBQADj84xG6UHQVcACAIAA9IernsBAAMTpqHWctxq0zYE', small_file_unique_id='AQADj84xG6UHQVcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:47,025 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:47,043 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:47,043 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:47,043 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:47,043 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:47,045 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:47,045 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:47,591 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'654'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:47,592 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:47,593 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:47,593 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:47,594 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:47,594 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:47,594 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('VSEV0L0D7',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='https://t.me/easy_roblox_bot?start=6022479099', first_name='🥵🥶V$EVØLØD🥶🥵', id=6022479099, photo=ChatPhoto(big_file_id='AQADAgADrPYxG7lMUUsACAMAA_u892YBAAPGraT21FPKxDYE', big_file_unique_id='AQADrPYxG7lMUUsB', small_file_id='AQADAgADrPYxG7lMUUsACAIAA_u892YBAAPGraT21FPKxDYE', small_file_unique_id='AQADrPYxG7lMUUsAAQ'), type=<ChatType.PRIVATE>, username='VSEV0L0D7')
2025-07-21 01:04:47,594 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:47,613 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:47,613 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:47,613 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:47,615 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:47,615 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:47,615 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:48,013 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'564'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:48,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:48,013 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:48,014 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:48,014 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:48,014 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:48,014 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Kunalrandive',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kunal', id=7222593793, last_name='1718', photo=ChatPhoto(big_file_id='AQADBQADL8QxG9JY-FYACAMAAwEJgK4BAAMWVToGu2G_tzYE', big_file_unique_id='AQADL8QxG9JY-FYB', small_file_id='AQADBQADL8QxG9JY-FYACAIAAwEJgK4BAAMWVToGu2G_tzYE', small_file_unique_id='AQADL8QxG9JY-FYAAQ'), type=<ChatType.PRIVATE>, username='Kunalrandive')
2025-07-21 01:04:48,014 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:48,031 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:48,031 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:48,031 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:48,033 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:48,033 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:48,033 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:04:48,582 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:34:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'481'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:04:48,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:04:48,585 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:04:48,585 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:04:48,585 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:04:48,585 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:04:48,585 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ro_45', id=1309658863, photo=ChatPhoto(big_file_id='AQADBQADTcMxGyMv4VYACAMAA-_OD04ABPD3XpYBmtBRNgQ', big_file_unique_id='AQADTcMxGyMv4VYB', small_file_id='AQADBQADTcMxGyMv4VYACAIAA-_OD04ABPD3XpYBmtBRNgQ', small_file_unique_id='AQADTcMxGyMv4VYAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:04:48,585 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:04:48,603 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:04:48,603 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:04:48,603 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:04:48,603 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:04:48,605 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:04:48,605 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
