2025-07-21 01:24:03,581 - __main__ - INFO - Starting robust cleanup: robust_1753041243_efab3f8a
2025-07-21 01:24:03,584 - __main__ - INFO - Connecting to database...
2025-07-21 01:24:16,715 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 01:24:18,790 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 01:24:18,870 - __main__ - INFO - Total users to process: 54,558
2025-07-21 01:24:18,892 - __main__ - INFO - Running preview with 100 users...
2025-07-21 01:24:19,228 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:24:19,230 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:24:19,625 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:24:19,626 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:24:20,021 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:24:20,030 - httpcore.connection - DEBUG - connect_tcp.started host='api.telegram.org' port=443 local_address=None timeout=5.0 socket_options=None
2025-07-21 01:24:20,236 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020D582E9C10>
2025-07-21 01:24:20,236 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020D582BE0D0> server_hostname='api.telegram.org' timeout=5.0
2025-07-21 01:24:20,447 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020D582E8CB0>
2025-07-21 01:24:20,448 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:24:20,448 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:24:20,448 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:24:20,448 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:24:20,449 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:24:21,004 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:54:21 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'280'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:24:21,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:24:21,006 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:24:21,007 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:24:21,007 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:24:21,007 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:24:21,008 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Merabeel', id=7551776079, last_name='Lucia', type=<ChatType.PRIVATE>)
2025-07-21 01:24:21,008 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:24:23,009 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:24:23,010 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:24:23,010 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:24:23,010 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:24:23,010 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:24:23,010 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:24:23,577 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:54:24 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'643'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:24:23,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:24:23,578 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:24:23,578 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:24:23,578 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:24:23,578 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:24:23,579 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('frshdmt',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'birthdate': {'day': 13, 'month': 3, 'year': 1997}, 'max_reaction_count': 11}, bio='@threeezhe', first_name='|𓁿Farshad Mt', has_private_forwards=True, id=1730562411, photo=ChatPhoto(big_file_id='AQADBAADjcYxG9zCcVEACAMAA2tJJmcABBciIWKuyoyXNgQ', big_file_unique_id='AQADjcYxG9zCcVEB', small_file_id='AQADBAADjcYxG9zCcVEACAIAA2tJJmcABBciIWKuyoyXNgQ', small_file_unique_id='AQADjcYxG9zCcVEAAQ'), type=<ChatType.PRIVATE>, username='frshdmt')
2025-07-21 01:24:23,579 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:24:25,579 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:24:25,580 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:24:25,580 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:24:25,580 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:24:25,581 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:24:25,581 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:24:26,131 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:54:26 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'399'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:24:26,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:24:26,132 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:24:26,132 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:24:26,132 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:24:26,132 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:24:26,132 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('alreadyuserisdead',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='👅', first_name='Yadhu', has_private_forwards=True, id=993133829, last_name='krishna', type=<ChatType.PRIVATE>, username='alreadyuserisdead')
2025-07-21 01:24:26,133 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:24:28,133 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:24:28,133 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:24:28,134 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:24:28,134 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:24:28,134 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:24:28,134 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:24:28,689 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:54:29 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'334'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:24:28,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:24:28,690 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:24:28,690 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:24:28,690 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:24:28,690 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:24:28,691 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('Bhawni888',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Bhawni', id=7693655059, last_name='Kumar', type=<ChatType.PRIVATE>, username='Bhawni888')
2025-07-21 01:24:28,691 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:24:30,691 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:24:30,691 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:24:30,692 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:24:30,692 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:24:30,692 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:24:30,692 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:24:31,235 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:54:31 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'253'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:24:31,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:24:31,236 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:24:31,236 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:24:31,236 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:24:31,237 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:24:31,237 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='H', id=7623719849, type=<ChatType.PRIVATE>)
2025-07-21 01:24:31,237 - telegram.Bot - DEBUG - Exiting: get_chat
