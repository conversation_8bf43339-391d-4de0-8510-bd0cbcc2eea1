2025-07-21 01:22:59,904 - __main__ - INFO - Starting robust cleanup: robust_1753041179_fc1c9fe5
2025-07-21 01:22:59,905 - __main__ - INFO - Connecting to database...
2025-07-21 01:23:12,566 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 01:23:14,288 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 01:23:14,353 - __main__ - INFO - Total users to process: 54,558
2025-07-21 01:23:14,368 - __main__ - INFO - Running preview with 100 users...
2025-07-21 01:23:14,537 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:23:14,538 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:23:14,977 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:23:14,978 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:23:15,513 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:15,524 - httpcore.connection - DEBUG - connect_tcp.started host='api.telegram.org' port=443 local_address=None timeout=5.0 socket_options=None
2025-07-21 01:23:15,727 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001BD3424AA20>
2025-07-21 01:23:15,728 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001BD3420B950> server_hostname='api.telegram.org' timeout=5.0
2025-07-21 01:23:15,933 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001BD33B51550>
2025-07-21 01:23:15,933 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:15,934 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:15,934 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:15,934 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:15,934 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:16,477 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:17 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'342'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:16,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:16,479 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:16,479 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:16,479 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:16,479 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:16,480 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('gautamxkr',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='gautam', has_private_forwards=True, id=1405169128, type=<ChatType.PRIVATE>, username='gautamxkr')
2025-07-21 01:23:16,480 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:18,483 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:18,483 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:18,483 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:18,483 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:18,483 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:18,483 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:19,029 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:19 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:19,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:19,031 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:19,031 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:19,031 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:19,031 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:19,031 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='vijay', id=6589593516, last_name='Singh', type=<ChatType.PRIVATE>)
2025-07-21 01:23:19,031 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:21,032 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:21,032 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:21,032 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:21,032 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:21,032 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:21,032 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:21,579 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:22 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'349'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:21,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:21,580 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:21,581 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:21,581 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:21,581 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:21,581 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('P_r_a_v_e_e_n_0',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Praveen', id=1454013564, last_name='Prakash', type=<ChatType.PRIVATE>, username='P_r_a_v_e_e_n_0')
2025-07-21 01:23:21,581 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:23,583 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:23,583 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:23,583 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:23,583 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:23,583 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:23,583 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:24,138 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:24 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'359'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:24,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:24,139 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:24,139 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:24,139 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:24,139 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:24,139 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('tejas048',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Aʟᴏɴᴇ', id=7772792179, last_name='Bᴏʏ', type=<ChatType.PRIVATE>, username='tejas048')
2025-07-21 01:23:24,139 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:26,140 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:26,140 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:26,140 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:26,140 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:26,140 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:26,140 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:26,520 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:27 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'314'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:26,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:26,522 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:26,522 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:26,522 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:26,522 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:26,522 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='https://t.me/+Bb8pVivxr1FlZjRl', first_name='Hari', id=6424593297, last_name='yadi', type=<ChatType.PRIVATE>)
2025-07-21 01:23:26,522 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:28,522 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:28,522 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:28,522 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:28,522 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:28,522 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:28,522 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:28,910 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:29 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'616'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:28,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:28,910 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:28,911 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:28,911 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:28,911 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:28,911 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Kkkkhh98',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Hai Năm Anh Nhập Ngũ', id=7277590586, last_name='Em Có Chờ Anh Không', photo=ChatPhoto(big_file_id='AQADBQADv8MxG8nUSVYACAMAAzo4x7EBAANIbKyRQL1rczYE', big_file_unique_id='AQADv8MxG8nUSVYB', small_file_id='AQADBQADv8MxG8nUSVYACAIAAzo4x7EBAANIbKyRQL1rczYE', small_file_unique_id='AQADv8MxG8nUSVYAAQ'), type=<ChatType.PRIVATE>, username='Kkkkhh98')
2025-07-21 01:23:28,911 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:30,912 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:30,912 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:30,912 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:30,912 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:30,913 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:30,913 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:31,512 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:32 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'618'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:31,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:31,512 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:31,514 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:31,514 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:31,514 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:31,514 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Riyafirin',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='https://www.myntra.com/jackets?f=Gender%3Aboys%2Cboys+girls&sort=disco', first_name='RAKIB', id=6237871390, photo=ChatPhoto(big_file_id='AQADBQADwMUxGy3HAVUACAMAAx5dznMBAANy0SIKqPLTGTYE', big_file_unique_id='AQADwMUxGy3HAVUB', small_file_id='AQADBQADwMUxGy3HAVUACAIAAx5dznMBAANy0SIKqPLTGTYE', small_file_unique_id='AQADwMUxGy3HAVUAAQ'), type=<ChatType.PRIVATE>, username='Riyafirin')
2025-07-21 01:23:31,514 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:33,514 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:33,515 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:33,515 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:33,515 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:33,516 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:33,516 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:34,096 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:34 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'536'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:34,096 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:34,096 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:34,096 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:34,097 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:34,097 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:34,097 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Mikeyuop',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rami', id=7517676075, photo=ChatPhoto(big_file_id='AQADBQAD88QxG1ek8FYACAMAAyuiFsABAAMDrLpQWppUrTYE', big_file_unique_id='AQAD88QxG1ek8FYB', small_file_id='AQADBQAD88QxG1ek8FYACAIAAyuiFsABAAMDrLpQWppUrTYE', small_file_unique_id='AQAD88QxG1ek8FYAAQ'), type=<ChatType.PRIVATE>, username='Mikeyuop')
2025-07-21 01:23:34,097 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:36,098 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:36,099 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:36,100 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:36,100 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:36,100 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:36,101 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:36,484 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:37 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'381'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:36,485 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:36,486 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:36,487 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:36,487 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:36,488 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:36,489 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Soumy_k005',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Insta:- dhruv___005 snap:- soumy_k005', first_name='Dhruv', id=5415100364, last_name='patel', type=<ChatType.PRIVATE>, username='Soumy_k005')
2025-07-21 01:23:36,489 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:38,491 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:38,492 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:38,492 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:38,492 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:38,492 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:38,492 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:38,888 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:39 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:38,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:38,889 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:38,889 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:38,889 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:38,889 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:38,890 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Mandip', id=7323945716, last_name='Kuta', type=<ChatType.PRIVATE>)
2025-07-21 01:23:38,890 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:40,891 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:40,891 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:40,892 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:40,892 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:40,892 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:40,892 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:41,294 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:41 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'561'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:41,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:41,295 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:41,295 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:41,295 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:41,296 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:41,296 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Gesibility',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='George', id=7809523075, last_name='Ibok', photo=ChatPhoto(big_file_id='AQADBAAD4McxG8sWwFIACAMAA4Pde9EBAAPw1d7gHmj3TjYE', big_file_unique_id='AQAD4McxG8sWwFIB', small_file_id='AQADBAAD4McxG8sWwFIACAIAA4Pde9EBAAPw1d7gHmj3TjYE', small_file_unique_id='AQAD4McxG8sWwFIAAQ'), type=<ChatType.PRIVATE>, username='Gesibility')
2025-07-21 01:23:41,296 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:43,297 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:43,297 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:43,298 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:43,298 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:43,298 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:43,298 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:43,837 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:44 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'434'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:43,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:43,837 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:43,838 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:43,838 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:43,838 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:43,838 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Amin_BamBayii',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'birthdate': {'day': 13, 'month': 6}, 'max_reaction_count': 11}, bio='خطا', first_name='Amin_BamBayi 🐱 MIAO', has_private_forwards=True, id=7293342359, type=<ChatType.PRIVATE>, username='Amin_BamBayii')
2025-07-21 01:23:43,838 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:45,839 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:45,840 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:45,840 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:45,840 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:45,840 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:45,840 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:46,220 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'338'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:46,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:46,220 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:46,220 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:46,220 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:46,221 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:46,221 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Rehankhan280',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rehan', id=7321793715, last_name='Khan', type=<ChatType.PRIVATE>, username='Rehankhan280')
2025-07-21 01:23:46,221 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:23:48,221 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:23:48,221 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:23:48,222 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:23:48,222 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:23:48,222 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:23:48,222 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:23:48,780 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:53:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'569'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:23:48,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:23:48,781 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:23:48,781 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:23:48,781 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:23:48,781 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:23:48,781 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Amelia_sheikh',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Amelia', id=7767394349, last_name='Sheikh', photo=ChatPhoto(big_file_id='AQADAQADa68xG0UwmUUACAMAAy0I-c4BAAOVjUkoVPCtwTYE', big_file_unique_id='AQADa68xG0UwmUUB', small_file_id='AQADAQADa68xG0UwmUUACAIAAy0I-c4BAAOVjUkoVPCtwTYE', small_file_unique_id='AQADa68xG0UwmUUAAQ'), type=<ChatType.PRIVATE>, username='Amelia_sheikh')
2025-07-21 01:23:48,781 - telegram.Bot - DEBUG - Exiting: get_chat
