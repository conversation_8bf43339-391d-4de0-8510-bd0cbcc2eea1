"""
Broadcast Performance Configuration
Optimized settings for different user scales and Telegram API limits
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BroadcastConfig:
    """Configuration class for broadcast performance optimization"""
    
    # Telegram API Rate Limits (official limits)
    TELEGRAM_RATE_LIMITS = {
        "messages_per_second": 30,  # 30 messages per second to different users
        "messages_per_minute": 1800,  # 30 * 60 = 1800 per minute
        "burst_limit": 30,  # Can send 30 messages immediately
    }
    
    # Performance configurations for different user scales
    PERFORMANCE_CONFIGS = {
        "small": {  # Up to 10,000 users
            "batch_size": 30,
            "concurrent_batches": 2,
            "delay_between_messages": 0.01,  # 10ms
            "delay_between_batches": 0.05,   # 50ms
            "progress_update_interval": 200,
            "expected_time_minutes": 6,  # ~6 minutes for 10k users
        },
        "medium": {  # 10,000 to 50,000 users
            "batch_size": 30,
            "concurrent_batches": 3,
            "delay_between_messages": 0.01,  # 10ms
            "delay_between_batches": 0.1,    # 100ms
            "progress_update_interval": 500,
            "expected_time_minutes": 25,  # ~25 minutes for 50k users
        },
        "large": {  # 50,000 to 100,000 users
            "batch_size": 30,
            "concurrent_batches": 4,
            "delay_between_messages": 0.01,  # 10ms
            "delay_between_batches": 0.1,    # 100ms
            "progress_update_interval": 1000,
            "expected_time_minutes": 50,  # ~50 minutes for 100k users
        },
        "xlarge": {  # 100,000+ users
            "batch_size": 30,
            "concurrent_batches": 5,
            "delay_between_messages": 0.01,  # 10ms
            "delay_between_batches": 0.15,   # 150ms (more conservative)
            "progress_update_interval": 2000,
            "expected_time_minutes": 100,  # ~100 minutes for 200k users
        }
    }
    
    @classmethod
    def get_config_for_user_count(cls, user_count: int) -> Dict[str, Any]:
        """Get optimal configuration based on user count"""
        if user_count <= 10000:
            config_name = "small"
        elif user_count <= 50000:
            config_name = "medium"
        elif user_count <= 100000:
            config_name = "large"
        else:
            config_name = "xlarge"
        
        config = cls.PERFORMANCE_CONFIGS[config_name].copy()
        config["config_name"] = config_name
        config["user_count"] = user_count
        
        # Calculate theoretical performance metrics
        messages_per_second = config["concurrent_batches"] * config["batch_size"] / (
            config["batch_size"] * config["delay_between_messages"] + config["delay_between_batches"]
        )
        
        config["theoretical_messages_per_second"] = round(messages_per_second, 2)
        config["estimated_completion_time_minutes"] = round(user_count / (messages_per_second * 60), 2)
        
        logger.info(f"Selected {config_name} config for {user_count} users: "
                   f"{config['theoretical_messages_per_second']} msg/sec, "
                   f"~{config['estimated_completion_time_minutes']} minutes")
        
        return config
    
    @classmethod
    def get_performance_expectations(cls) -> Dict[str, Dict[str, Any]]:
        """Get performance expectations for different user counts"""
        expectations = {}
        
        test_counts = [1000, 5000, 10000, 25000, 50000, 75000, 100000, 200000]
        
        for count in test_counts:
            config = cls.get_config_for_user_count(count)
            expectations[f"{count}_users"] = {
                "user_count": count,
                "config_used": config["config_name"],
                "estimated_time_minutes": config["estimated_completion_time_minutes"],
                "estimated_time_hours": round(config["estimated_completion_time_minutes"] / 60, 2),
                "messages_per_second": config["theoretical_messages_per_second"],
                "concurrent_batches": config["concurrent_batches"],
                "batch_size": config["batch_size"]
            }
        
        return expectations

class BroadcastPerformanceMonitor:
    """Monitor and track broadcast performance metrics"""
    
    def __init__(self):
        self.start_time = None
        self.processed_count = 0
        self.total_users = 0
        self.successful_sends = 0
        self.failed_sends = 0
        self.current_rate = 0.0
        
    def start_monitoring(self, total_users: int):
        """Start performance monitoring"""
        import time
        self.start_time = time.time()
        self.total_users = total_users
        self.processed_count = 0
        self.successful_sends = 0
        self.failed_sends = 0
        
    def update_progress(self, processed: int, successful: int, failed: int):
        """Update progress metrics"""
        import time
        if not self.start_time:
            return
            
        self.processed_count = processed
        self.successful_sends = successful
        self.failed_sends = failed
        
        elapsed_time = time.time() - self.start_time
        if elapsed_time > 0:
            self.current_rate = processed / elapsed_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        import time
        if not self.start_time:
            return {}
            
        elapsed_time = time.time() - self.start_time
        progress_percentage = (self.processed_count / self.total_users * 100) if self.total_users > 0 else 0
        
        remaining_users = self.total_users - self.processed_count
        estimated_remaining_time = (remaining_users / self.current_rate) if self.current_rate > 0 else 0
        
        return {
            "elapsed_time_seconds": round(elapsed_time, 2),
            "elapsed_time_minutes": round(elapsed_time / 60, 2),
            "processed_count": self.processed_count,
            "total_users": self.total_users,
            "progress_percentage": round(progress_percentage, 2),
            "current_rate_per_second": round(self.current_rate, 2),
            "current_rate_per_minute": round(self.current_rate * 60, 2),
            "successful_sends": self.successful_sends,
            "failed_sends": self.failed_sends,
            "success_rate_percentage": round((self.successful_sends / self.processed_count * 100) if self.processed_count > 0 else 0, 2),
            "estimated_remaining_time_minutes": round(estimated_remaining_time / 60, 2),
            "estimated_total_time_minutes": round((elapsed_time + estimated_remaining_time) / 60, 2)
        }
    
    def log_performance_summary(self):
        """Log final performance summary"""
        stats = self.get_performance_stats()
        logger.info(f"Broadcast Performance Summary:")
        logger.info(f"  Total Users: {stats.get('total_users', 0)}")
        logger.info(f"  Processed: {stats.get('processed_count', 0)}")
        logger.info(f"  Success Rate: {stats.get('success_rate_percentage', 0)}%")
        logger.info(f"  Average Rate: {stats.get('current_rate_per_second', 0)} msg/sec")
        logger.info(f"  Total Time: {stats.get('elapsed_time_minutes', 0)} minutes")

# Global performance monitor instance
performance_monitor = BroadcastPerformanceMonitor()
