2025-07-21 01:05:15,251 - __main__ - INFO - Starting background cleanup process: full_cleanup_001
2025-07-21 01:05:15,254 - __main__ - INFO - Connecting to database...
2025-07-21 01:05:28,021 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 01:05:29,682 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 01:05:29,684 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:05:29,685 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:05:30,052 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 01:05:30,055 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem'
2025-07-21 01:05:30,545 - __main__ - INFO - Running full cleanup...
2025-07-21 01:05:30,547 - services.database_cleanup_service - INFO - Starting database cleanup process
2025-07-21 01:05:31,647 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,657 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,658 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,659 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,659 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,660 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,660 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,660 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,661 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,661 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:31,663 - httpcore.connection - DEBUG - connect_tcp.started host='api.telegram.org' port=443 local_address=None timeout=5.0 socket_options=None
2025-07-21 01:05:31,868 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001D9E49A0770>
2025-07-21 01:05:31,868 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001D9E4837250> server_hostname='api.telegram.org' timeout=5.0
2025-07-21 01:05:32,074 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001D9E43F9D90>
2025-07-21 01:05:32,075 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,075 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:32,075 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:32,076 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:32,076 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,467 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:33 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'2031'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:32,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:32,469 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:32,469 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:32,469 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:32,470 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:32,470 - telegram.Bot - DEBUG - Chat(accent_color_id=11, active_usernames=('kevinbacks', 'agentsir'), api_kwargs={'can_send_gift': True, 'business_intro': {'title': '𝐇𝐞𝐥𝐥𝐨 𝐁𝐎𝐒𝐒', 'message': '𝐡𝐨𝐰 𝐜𝐚𝐧 𝐢 𝐡𝐞𝐥𝐩 𝐲𝐨𝐮 ?', 'sticker': {'width': 512, 'height': 512, 'emoji': '🚬', 'set_name': 'PBVid', 'is_animated': False, 'is_video': True, 'type': 'regular', 'thumbnail': {'file_id': 'AAMCBAADFQABaH09oG3bbX2cshoXCW1gaVVEra8AArEKAAJPfZFTGXrCrHOzSnUBAAdtAAM2BA', 'file_unique_id': 'AQADsQoAAk99kVNy', 'file_size': 8998, 'width': 320, 'height': 320}, 'thumb': {'file_id': 'AAMCBAADFQABaH09oG3bbX2cshoXCW1gaVVEra8AArEKAAJPfZFTGXrCrHOzSnUBAAdtAAM2BA', 'file_unique_id': 'AQADsQoAAk99kVNy', 'file_size': 8998, 'width': 320, 'height': 320}, 'file_id': 'CAACAgQAAxUAAWh9PaBt2219nLIaFwltYGlVRK2vAAKxCgACT32RUxl6wqxzs0p1NgQ', 'file_unique_id': 'AgADsQoAAk99kVM', 'file_size': 208753}}, 'business_opening_hours': {'opening_hours': [{'opening_minute': 720, 'closing_minute': 1320}, {'opening_minute': 2160, 'closing_minute': 2760}, {'opening_minute': 3600, 'closing_minute': 4200}, {'opening_minute': 5040, 'closing_minute': 5640}, {'opening_minute': 6600, 'closing_minute': 6990}], 'time_zone_name': 'Asia/Kolkata'}, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, background_custom_emoji_id='5298532944976034951', bio='A great person attracts great people and knows how to hold them together:- https://t.me/+q7eJGUQIXL40NTk1', emoji_status_custom_emoji_id='5983135596493672471', first_name='Kêviñ', id=2027123358, photo=ChatPhoto(big_file_id='AQADBQADsrExG1iZ2VQACAMAA55y03gABDz8rnC2fzjNNgQ', big_file_unique_id='AQADsrExG1iZ2VQB', small_file_id='AQADBQADsrExG1iZ2VQACAIAA55y03gABDz8rnC2fzjNNgQ', small_file_unique_id='AQADsrExG1iZ2VQAAQ'), type=<ChatType.PRIVATE>, username='kevinbacks')
2025-07-21 01:05:32,471 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:32,472 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,472 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:32,473 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:32,473 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:32,473 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,491 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:32,861 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:33 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'775'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:32,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:32,862 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:32,862 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:32,862 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:32,863 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:32,863 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('the_titanium_admin',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002414699235, 'title': 'TITANIUM BOTS⚡️', 'username': 'titanium_bots_channel', 'type': 'channel'}, 'max_reaction_count': 11}, bio='dont send hi, hello... write full query (else no reply)', first_name='Titanium Bots', id=8153676253, last_name='Admin', photo=ChatPhoto(big_file_id='AQADBQADEMExGyuh0FYACAMAA905_-UBAAMF-9dgTTJNrTYE', big_file_unique_id='AQADEMExGyuh0FYB', small_file_id='AQADBQADEMExGyuh0FYACAIAA905_-UBAAMF-9dgTTJNrTYE', small_file_unique_id='AQADEMExGyuh0FYAAQ'), type=<ChatType.PRIVATE>, username='the_titanium_admin')
2025-07-21 01:05:32,864 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:32,864 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,865 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:32,865 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:32,866 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:32,867 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:32,886 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:33,257 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:33 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'334'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:33,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:33,258 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:33,258 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:33,258 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:33,258 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:33,259 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Bruhmr',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Diljo', id=5434457364, last_name='👀', type=<ChatType.PRIVATE>, username='Bruhmr')
2025-07-21 01:05:33,259 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:33,259 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:33,260 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:33,260 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:33,260 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:33,260 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:33,278 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:33,652 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:34 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'507'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:33,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:33,653 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:33,653 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:33,654 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:33,654 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:33,654 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='1157611140', id=1843145973, last_name='Xilobi', photo=ChatPhoto(big_file_id='AQADBQADJK8xG4fYEFcACAMAA_Us3G0ABANrS4m0ir23NgQ', big_file_unique_id='AQADJK8xG4fYEFcB', small_file_id='AQADBQADJK8xG4fYEFcACAIAA_Us3G0ABANrS4m0ir23NgQ', small_file_unique_id='AQADJK8xG4fYEFcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:05:33,654 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:33,655 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:33,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:33,655 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:33,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:33,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:33,673 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:34,038 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:34 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'276'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:34,039 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:34,060 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,061 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:34,061 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:34,062 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:34,063 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Abhiram', id=6541021580, last_name='.p', type=<ChatType.PRIVATE>)
2025-07-21 01:05:34,063 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:34,065 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,067 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:34,067 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,068 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:34,069 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,090 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:34,452 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:35 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'304'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:34,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:34,454 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,455 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:34,455 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:34,455 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:34,456 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Abhiram', has_private_forwards=True, id=6175826451, last_name='.P', type=<ChatType.PRIVATE>)
2025-07-21 01:05:34,457 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:34,458 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,459 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:34,459 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,460 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:34,460 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,479 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:34,853 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:35 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'877'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:34,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:34,855 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,855 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:34,855 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:34,856 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:34,856 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('ChillRyan',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002237118163, 'title': '𝐀𝐆𝐄𝐍𝐓𝐒 𝐓𝐄𝐀𝐌', 'username': 'AGENTS_TEAM00', 'type': 'channel'}, 'max_reaction_count': 11}, bio='"Leadership is the capacity to translate vision into reality."', first_name='𝐑 𝐘 𝐀 𝐍 ♛', id=1363710641, photo=ChatPhoto(big_file_id='AQADBQADCsYxGw1UQFUACAMAA7GSSFEABIxsG_OqUYb8NgQ', big_file_unique_id='AQADCsYxGw1UQFUB', small_file_id='AQADBQADCsYxGw1UQFUACAIAA7GSSFEABIxsG_OqUYb8NgQ', small_file_unique_id='AQADCsYxGw1UQFUAAQ'), type=<ChatType.PRIVATE>, username='ChillRyan')
2025-07-21 01:05:34,857 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:34,857 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,858 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:34,858 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:34,859 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:34,859 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:34,860 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:35,241 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:35 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'656'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:35,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:35,243 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:35,243 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:35,243 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:35,243 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:35,244 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ben10', id=5601436882, last_name='ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ', type=<ChatType.PRIVATE>)
2025-07-21 01:05:35,244 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:35,244 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:35,245 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:35,245 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:35,245 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:35,245 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:35,263 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:35,631 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:36 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'350'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:35,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:35,633 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:35,633 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:35,633 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:35,633 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:35,633 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('Its_kittu244',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Hiring Good Agents!! DM', first_name='Guru', id=6587042657, type=<ChatType.PRIVATE>, username='Its_kittu244')
2025-07-21 01:05:35,633 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:35,635 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:35,635 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:35,635 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:35,635 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:35,635 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:35,652 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:36,081 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:36 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:36,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:36,082 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,082 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:36,082 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:36,082 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:36,082 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Katrina', id=6647081461, last_name='Kaif', type=<ChatType.PRIVATE>)
2025-07-21 01:05:36,084 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:36,084 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,084 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:36,084 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,085 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:36,085 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,103 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:36,465 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:37 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'264'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:36,465 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:36,465 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:36,467 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Navalkishore', id=6014460632, type=<ChatType.PRIVATE>)
2025-07-21 01:05:36,467 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:36,467 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,469 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:36,854 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:37 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'502'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:36,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:36,855 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Aniruddh', id=5012792512, last_name='M', photo=ChatPhoto(big_file_id='AQADBQADJ7wxG_dVWFQACAMAA8AkySoBAAMpw3sz2x6hPjYE', big_file_unique_id='AQADJ7wxG_dVWFQB', small_file_id='AQADBQADJ7wxG_dVWFQACAIAA8AkySoBAAMpw3sz2x6hPjYE', small_file_unique_id='AQADJ7wxG_dVWFQAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:05:36,855 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:36,855 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:36,856 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:36,856 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:36,875 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:37,242 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:37 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'507'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:37,242 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:37,242 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:37,242 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:37,244 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:37,244 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:37,244 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Baddd🤪🤪', id=5428753743, photo=ChatPhoto(big_file_id='AQADBQADscIxG6-aaVYACAMAA081lEMBAAOPE4-lqiHOZDYE', big_file_unique_id='AQADscIxG6-aaVYB', small_file_id='AQADBQADscIxG6-aaVYACAIAA081lEMBAAOPE4-lqiHOZDYE', small_file_unique_id='AQADscIxG6-aaVYAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:05:37,244 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:37,244 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:37,245 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:37,245 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:37,245 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:37,245 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:37,264 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:37,623 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:38 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'308'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:37,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:37,623 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:37,623 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:37,623 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:37,623 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:37,623 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Aj116809',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='..', id=1124538066, type=<ChatType.PRIVATE>, username='Aj116809')
2025-07-21 01:05:37,625 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:37,625 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:37,625 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:37,625 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:37,625 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:37,625 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:37,645 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:38,006 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:38 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'259'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:38,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:38,008 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,008 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:38,008 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:38,008 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:38,008 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Anirudh', id=5259350225, type=<ChatType.PRIVATE>)
2025-07-21 01:05:38,008 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:38,010 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,010 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:38,010 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,010 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:38,010 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,029 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:38,390 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:38 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'257'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:38,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:38,391 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,391 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:38,391 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:38,391 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:38,391 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kalam', id=6946190587, type=<ChatType.PRIVATE>)
2025-07-21 01:05:38,393 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:38,393 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,393 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:38,394 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,394 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:38,394 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,413 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:38,775 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:39 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'287'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:38,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:38,776 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,776 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:38,776 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:38,776 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:38,776 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='...🐾', id=1664717519, last_name='(+_+)', type=<ChatType.PRIVATE>)
2025-07-21 01:05:38,776 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:38,778 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,778 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:38,778 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:38,778 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:38,778 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:38,798 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:39,163 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:39 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'505'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:39,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:39,163 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,163 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:39,163 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:39,165 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:39,165 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Niranj', id=6733373790, last_name='Dinesh', photo=ChatPhoto(big_file_id='AQADBQADXMExG8824VQACAMAA14hV5EBAAOzwNtkpgKx-jYE', big_file_unique_id='AQADXMExG8824VQB', small_file_id='AQADBQADXMExG8824VQACAIAA14hV5EBAAOzwNtkpgKx-jYE', small_file_unique_id='AQADXMExG8824VQAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:05:39,165 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:39,166 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,166 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:39,166 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,166 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:39,166 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,185 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:39,546 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:40 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'272'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:39,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:39,548 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,549 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:39,549 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:39,550 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:39,551 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Adi', id=7280067661, last_name='Th', type=<ChatType.PRIVATE>)
2025-07-21 01:05:39,551 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:39,552 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,553 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:39,553 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,554 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:39,554 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,573 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:39,924 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:40 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'315'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:39,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:39,926 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,926 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:39,926 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:39,926 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:39,926 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('sharuuhhh1',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Kichu', id=6585454780, type=<ChatType.PRIVATE>, username='sharuuhhh1')
2025-07-21 01:05:39,926 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:39,928 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,928 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:39,928 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:39,929 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:39,929 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:39,947 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:40,302 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:40 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'258'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:40,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:40,303 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:40,303 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:40,303 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:40,304 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:40,304 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Nishad', id=1911375973, type=<ChatType.PRIVATE>)
2025-07-21 01:05:40,304 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:40,305 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:40,306 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:40,306 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:40,307 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:40,307 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:40,326 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:40,693 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:41 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'620'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:40,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:40,695 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:40,695 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:40,695 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:40,696 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:40,696 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Em_ex8',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Ethereum’s interoperability layer ☕️ Espresso', first_name='MX 8', id=1266530826, last_name='☕', photo=ChatPhoto(big_file_id='AQADBQAD2L8xGxwsiVUACAMAAwq6fUsABO4C5iLzPJw2NgQ', big_file_unique_id='AQAD2L8xGxwsiVUB', small_file_id='AQADBQAD2L8xGxwsiVUACAIAAwq6fUsABO4C5iLzPJw2NgQ', small_file_unique_id='AQAD2L8xGxwsiVUAAQ'), type=<ChatType.PRIVATE>, username='Em_ex8')
2025-07-21 01:05:40,697 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:40,698 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:40,700 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:40,701 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:40,702 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:40,703 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:40,703 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:41,094 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:41 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'280'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:41,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:41,095 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,095 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:41,096 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:41,096 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:41,097 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Muhammed', id=5292781345, last_name='Iqbal', type=<ChatType.PRIVATE>)
2025-07-21 01:05:41,097 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:41,098 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,099 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:41,100 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,101 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:41,101 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,122 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:41,478 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'339'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:41,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:41,478 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,478 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:41,478 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:41,478 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:41,480 - telegram.Bot - DEBUG - Chat(accent_color_id=5, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='✨ every time I see you I fall in love with you eyes 👀', first_name='Charan...', id=5407426659, type=<ChatType.PRIVATE>)
2025-07-21 01:05:41,480 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:41,480 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,482 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:41,482 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,482 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:41,482 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,487 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:41,862 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:41,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:41,863 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,863 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:41,863 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:41,864 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:41,864 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sifat', id=7017414756, last_name='Siffat', type=<ChatType.PRIVATE>)
2025-07-21 01:05:41,864 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:41,866 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,867 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:41,868 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:41,868 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:41,868 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:41,886 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:42,261 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'309'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:42,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:42,261 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:42,261 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:42,263 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:42,263 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:42,263 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Aniruddha', has_private_forwards=True, id=1093982618, last_name='Murge', type=<ChatType.PRIVATE>)
2025-07-21 01:05:42,263 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:42,264 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:42,265 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:42,265 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:42,266 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:42,266 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:42,268 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:42,653 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:43 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'319'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:42,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:42,653 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:42,654 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:42,654 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:42,654 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:42,654 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('sre_n_nd',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ṣreenand', id=2084020521, type=<ChatType.PRIVATE>, username='sre_n_nd')
2025-07-21 01:05:42,656 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:42,656 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:42,658 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:42,658 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:42,658 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:42,658 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:42,677 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:43,043 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:43 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'329'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:43,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:43,045 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,045 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:43,045 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:43,045 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:43,045 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('alshanjr',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Alshan', id=6221145606, last_name='Jr', type=<ChatType.PRIVATE>, username='alshanjr')
2025-07-21 01:05:43,046 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:43,046 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,048 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:43,048 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,048 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:43,048 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,070 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:43,429 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:44 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'276'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:43,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:43,431 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,431 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:43,431 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:43,433 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:43,433 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Hard', id=6659921488, last_name='Devil', type=<ChatType.PRIVATE>)
2025-07-21 01:05:43,434 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:43,435 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,435 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:43,435 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,437 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:43,437 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,437 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:43,807 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:44 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'235'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:43,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:43,807 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,807 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:43,807 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:43,807 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:43,809 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'accepted_gift_types': {'unlimited_gifts': False, 'limited_gifts': False, 'unique_gifts': False, 'premium_subscription': False}, 'max_reaction_count': 11}, id=6343082682, type=<ChatType.PRIVATE>)
2025-07-21 01:05:43,809 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:43,809 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,809 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:43,809 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:43,811 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:43,811 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:43,830 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:44,194 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:44 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'570'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:44,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:44,194 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,194 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:44,194 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:44,196 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:44,196 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Guudjochoc',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Himanshu PATIL', id=7460080140, last_name='Patil', photo=ChatPhoto(big_file_id='AQADBQADX8ExG-ngMFcACAMAAwzKp7wBAAOzlxi_0Vk-DjYE', big_file_unique_id='AQADX8ExG-ngMFcB', small_file_id='AQADBQADX8ExG-ngMFcACAIAAwzKp7wBAAOzlxi_0Vk-DjYE', small_file_unique_id='AQADX8ExG-ngMFcAAQ'), type=<ChatType.PRIVATE>, username='Guudjochoc')
2025-07-21 01:05:44,196 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:44,196 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,197 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:44,197 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,197 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:44,197 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,217 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:44,587 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:45 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'351'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:44,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:44,587 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,587 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:44,587 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:44,587 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:44,589 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('FARID_KHNN',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='FARID', id=6004826870, last_name='JEHTANIA 😎', type=<ChatType.PRIVATE>, username='FARID_KHNN')
2025-07-21 01:05:44,589 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:44,589 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,589 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:44,589 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,589 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:44,589 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,609 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:44,971 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:45 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'343'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:44,971 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:44,971 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,971 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:44,971 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:44,971 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:44,972 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('JETGOD_1M',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='HELP ME 🥺', first_name='FROZEN', id=5981204991, type=<ChatType.PRIVATE>, username='JETGOD_1M')
2025-07-21 01:05:44,972 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:44,972 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,972 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:44,972 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:44,972 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:44,972 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:44,990 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:45,362 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:45 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'325'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:45,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:45,362 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:45,362 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:45,362 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:45,363 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:45,363 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Zekrom77',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='zzxs', first_name='Zekrom', id=6160841244, type=<ChatType.PRIVATE>, username='Zekrom77')
2025-07-21 01:05:45,363 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:45,363 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:45,363 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:45,363 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:45,365 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:45,365 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:45,383 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:45,782 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'258'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:45,784 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:45,784 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:45,784 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:45,784 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:45,784 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:45,784 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Randip', id=7344568279, type=<ChatType.PRIVATE>)
2025-07-21 01:05:45,784 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:45,785 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:45,785 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:45,785 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:45,786 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:45,786 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:45,804 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:46,164 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'278'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:46,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:46,166 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,166 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:46,166 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:46,166 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:46,167 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Lintan', id=6384565635, last_name='Rabha', type=<ChatType.PRIVATE>)
2025-07-21 01:05:46,167 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:46,167 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,168 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:46,168 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,168 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:46,168 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,186 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:46,549 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:47 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'257'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:46,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:46,550 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,551 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:46,551 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:46,551 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:46,552 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Shadn', id=6550071378, type=<ChatType.PRIVATE>)
2025-07-21 01:05:46,552 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:46,553 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,555 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:46,555 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,556 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:46,556 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,575 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:46,944 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:47 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'483'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:46,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:46,945 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,946 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:46,946 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:46,946 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:46,947 - telegram.Bot - DEBUG - Chat(accent_color_id=6, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rohit', id=6252438716, photo=ChatPhoto(big_file_id='AQADBQADh8kxG6oKQVcACAMAA7ykrHQBAAM38iwhCkWDEDYE', big_file_unique_id='AQADh8kxG6oKQVcB', small_file_id='AQADBQADh8kxG6oKQVcACAIAA7ykrHQBAAM38iwhCkWDEDYE', small_file_unique_id='AQADh8kxG6oKQVcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:05:46,948 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:46,950 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,951 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:46,951 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:46,952 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:46,952 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:46,953 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:47,338 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:47 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'349'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:47,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:47,339 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:47,340 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:47,340 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:47,340 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:47,340 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('biensanjQY',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='oneofmyzz4', id=7924488280, last_name='arulmurugan641', type=<ChatType.PRIVATE>, username='biensanjQY')
2025-07-21 01:05:47,340 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:47,340 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:47,342 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:47,342 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:47,342 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:47,342 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:47,343 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:47,727 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'339'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:47,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:47,727 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:47,727 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:47,728 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:47,728 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:47,728 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('COOLTECHIE25',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Cool', id=6415982162, last_name='Techie', type=<ChatType.PRIVATE>, username='COOLTECHIE25')
2025-07-21 01:05:47,728 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:47,728 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:47,730 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:47,730 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:47,732 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:47,732 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:47,734 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:48,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'921'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:48,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:48,159 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,159 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:48,159 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:48,159 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:48,160 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('MrAyushh',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'birthdate': {'day': 13, 'month': 7, 'year': 2000}, 'personal_chat': {'id': -1001456225285, 'title': 'PayTM Verified BOTS™', 'username': 'PayTMVerifiedBOTS', 'type': 'channel'}, 'max_reaction_count': 11}, bio='🇮🇳 Bot Coder | Founder Of @PayTMVerifiedBOTS | Bio : @Mr_Ayush_Bio', first_name='×͜× 𝗠𝗿 𝗔𝘆𝘂𝘀𝗵 [ LazZzy 💤 ]', id=783073136, photo=ChatPhoto(big_file_id='AQADBQAD-6cxG3C_rC4ACAMAA3C_rC4ABGb2sSH66oFhNgQ', big_file_unique_id='AQAD-6cxG3C_rC4B', small_file_id='AQADBQAD-6cxG3C_rC4ACAIAA3C_rC4ABGb2sSH66oFhNgQ', small_file_unique_id='AQAD-6cxG3C_rC4AAQ'), type=<ChatType.PRIVATE>, username='MrAyushh')
2025-07-21 01:05:48,160 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:48,160 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,160 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:48,162 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,162 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:48,162 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,180 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:48,586 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'390'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:48,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:48,587 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,587 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:48,587 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:48,587 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:48,588 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Lootskiller',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio=',/ longing to be rich', first_name='...', has_private_forwards=True, id=5760040578, last_name='//', type=<ChatType.PRIVATE>, username='Lootskiller')
2025-07-21 01:05:48,588 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:48,589 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,590 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:48,590 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,590 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:48,590 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,608 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:48,974 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'347'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:48,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:48,975 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,975 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:48,975 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:48,976 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:48,976 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Userrnnaamm',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Clegane', has_private_forwards=True, id=6117107757, type=<ChatType.PRIVATE>, username='Userrnnaamm')
2025-07-21 01:05:48,977 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:48,978 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,978 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:48,978 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:48,979 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:48,979 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:48,997 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:49,379 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'283'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:49,379 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:49,379 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:49,379 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:49,380 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:49,380 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:49,381 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Vinyak', id=6018905896, last_name='Kalligudda', type=<ChatType.PRIVATE>)
2025-07-21 01:05:49,381 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:49,382 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:49,382 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:49,382 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:49,382 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:49,382 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:49,401 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:49,771 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:50 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'625'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:49,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:49,771 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:49,773 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:49,773 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:49,773 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:49,773 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Wsmok1',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'birthdate': {'day': 30, 'month': 7}, 'max_reaction_count': 11}, bio='this account did not promote any illegal activity', first_name='Waseem', id=**********, photo=ChatPhoto(big_file_id='AQADBQADUr4xG5IR2VcACAMAA_9j7oMBAAOTVpB1wbzUyjYE', big_file_unique_id='AQADUr4xG5IR2VcB', small_file_id='AQADBQADUr4xG5IR2VcACAIAA_9j7oMBAAOTVpB1wbzUyjYE', small_file_unique_id='AQADUr4xG5IR2VcAAQ'), type=<ChatType.PRIVATE>, username='Wsmok1')
2025-07-21 01:05:49,775 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:49,775 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:49,775 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:49,775 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:49,775 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:49,775 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:49,794 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:50,179 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:50 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:50,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:50,180 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,180 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:50,180 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:50,180 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:50,180 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rohan', id=6311908626, last_name='Kumar', type=<ChatType.PRIVATE>)
2025-07-21 01:05:50,180 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:50,182 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,182 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:50,182 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,182 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:50,182 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,199 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:50,577 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:51 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'260'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:50,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:50,579 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,579 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:50,579 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:50,579 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:50,581 - telegram.Bot - DEBUG - Chat(accent_color_id=2, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Viinnnie', id=7950693424, type=<ChatType.PRIVATE>)
2025-07-21 01:05:50,581 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:50,581 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,582 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:50,583 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,583 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:50,583 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,601 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:50,963 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:51 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'322'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:50,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:50,965 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,965 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:50,965 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:50,965 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:50,965 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('Divyansh0000',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Divyansh', id=7696127533, type=<ChatType.PRIVATE>, username='Divyansh0000')
2025-07-21 01:05:50,965 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:50,967 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,967 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:50,967 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:50,967 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:50,967 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:50,969 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:51,362 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:51 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'545'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:51,364 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:51,364 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:51,364 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:51,364 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:51,365 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:51,365 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Aleexxxking',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Alexxxx', id=7488530785, photo=ChatPhoto(big_file_id='AQADBAADw8QxG5ScQFEACAMAA2HpWb4BAAOVRs9rDk_n1jYE', big_file_unique_id='AQADw8QxG5ScQFEB', small_file_id='AQADBAADw8QxG5ScQFEACAIAA2HpWb4BAAOVRs9rDk_n1jYE', small_file_unique_id='AQADw8QxG5ScQFEAAQ'), type=<ChatType.PRIVATE>, username='Aleexxxking')
2025-07-21 01:05:51,365 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:51,365 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:51,367 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:51,367 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:51,368 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:51,368 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:51,386 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:51,765 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:52 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:51,765 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:51,765 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:51,767 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:51,767 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:51,767 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:51,768 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rehan', id=8179710689, last_name='Ahmad', type=<ChatType.PRIVATE>)
2025-07-21 01:05:51,768 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:51,768 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:51,768 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:51,769 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:51,769 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:51,769 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:51,789 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:52,158 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:52 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'543'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:52,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:52,159 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:52,159 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:52,160 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:52,160 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:52,160 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Thonosop',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Thanøs', id=7315054698, photo=ChatPhoto(big_file_id='AQADBAADC8IxGwzgQFEACAMAA2rgArQBAAPEY3lZfbQ62TYE', big_file_unique_id='AQADC8IxGwzgQFEB', small_file_id='AQADBAADC8IxGwzgQFEACAIAA2rgArQBAAPEY3lZfbQ62TYE', small_file_unique_id='AQADC8IxGwzgQFEAAQ'), type=<ChatType.PRIVATE>, username='Thonosop')
2025-07-21 01:05:52,160 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:52,161 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:52,161 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:52,162 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:52,162 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:52,162 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:52,180 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:52,557 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:53 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'580'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:52,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:52,558 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:52,558 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:52,558 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:52,559 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:52,559 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Black_panther_0p',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Black', id=7108498689, last_name='Panthèr', photo=ChatPhoto(big_file_id='AQADBAADf8QxG9vnOVEACAMAAwEVs6cBAAPChrSvgErAITYE', big_file_unique_id='AQADf8QxG9vnOVEB', small_file_id='AQADBAADf8QxG9vnOVEACAIAAwEVs6cBAAPChrSvgErAITYE', small_file_unique_id='AQADf8QxG9vnOVEAAQ'), type=<ChatType.PRIVATE>, username='Black_panther_0p')
2025-07-21 01:05:52,559 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:52,561 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:52,561 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:52,561 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:52,562 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:52,562 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:52,564 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:53,023 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:53 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'567'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:53,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:53,024 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,024 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:53,025 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:53,025 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:53,025 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Bat_men00p',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Bàt', id=7056378145, last_name='Màn', photo=ChatPhoto(big_file_id='AQADBAADvsQxG8MdOVEACAMAAyHJl6QBAAO1fVCnFBmX3jYE', big_file_unique_id='AQADvsQxG8MdOVEB', small_file_id='AQADBAADvsQxG8MdOVEACAIAAyHJl6QBAAO1fVCnFBmX3jYE', small_file_unique_id='AQADvsQxG8MdOVEAAQ'), type=<ChatType.PRIVATE>, username='Bat_men00p')
2025-07-21 01:05:53,025 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:53,026 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,026 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:53,027 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,027 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:53,027 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,045 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:53,477 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:54 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'351'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:53,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:53,478 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,478 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:53,478 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:53,478 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:53,479 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('VANSH117',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='I AM A TRADER', first_name='Aura', id=7473809394, last_name='999+', type=<ChatType.PRIVATE>, username='VANSH117')
2025-07-21 01:05:53,479 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:53,479 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,480 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:53,480 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,480 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:53,481 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,499 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:53,866 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:54 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'544'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:53,867 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:53,867 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,867 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:53,867 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:53,867 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:53,868 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('Amandeepp0',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Amandeep', id=6460946131, photo=ChatPhoto(big_file_id='AQADBQAD5sUxG5QuuFYACAMAA9M2GoEBAAMz3I2NExZ3PzYE', big_file_unique_id='AQAD5sUxG5QuuFYB', small_file_id='AQADBQAD5sUxG5QuuFYACAIAA9M2GoEBAAMz3I2NExZ3PzYE', small_file_unique_id='AQAD5sUxG5QuuFYAAQ'), type=<ChatType.PRIVATE>, username='Amandeepp0')
2025-07-21 01:05:53,868 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:53,868 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,869 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:53,869 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:53,869 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:53,869 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:53,887 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:54,314 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:54 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'299'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:54,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:54,316 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:54,316 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:54,316 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:54,316 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:54,316 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Please add Karo', first_name='Vijay', id=7770696577, last_name='Nag', type=<ChatType.PRIVATE>)
2025-07-21 01:05:54,317 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:54,317 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:54,317 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:54,317 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:54,318 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:54,318 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:54,335 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:54,702 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:55 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'544'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:54,702 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:54,703 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:54,703 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:54,703 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:54,703 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:54,703 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Sharukhxxx',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Shahrukh', id=7400329021, photo=ChatPhoto(big_file_id='AQADBQADA8ExGzDcsFYACAMAAz0PGLkBAAPehTOghxLkOjYE', big_file_unique_id='AQADA8ExGzDcsFYB', small_file_id='AQADBQADA8ExGzDcsFYACAIAAz0PGLkBAAPehTOghxLkOjYE', small_file_unique_id='AQADA8ExGzDcsFYAAQ'), type=<ChatType.PRIVATE>, username='Sharukhxxx')
2025-07-21 01:05:54,703 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:54,704 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:54,704 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:54,704 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:54,704 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:54,704 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:54,722 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:55,101 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:55 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'402'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:55,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:55,102 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,102 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:55,103 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:55,103 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:55,103 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('The_akki_090',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ãkki 🐾 ၊||၊ DeSpeed ၊||၊', has_private_forwards=True, id=1939734296, type=<ChatType.PRIVATE>, username='The_akki_090')
2025-07-21 01:05:55,104 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:55,104 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:55,105 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:55,105 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,106 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:55,106 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:55,124 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:55,489 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:56 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'545'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:55,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:55,490 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,491 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:55,491 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:55,491 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:55,492 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('jang_cook_op',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Abroz', id=7101872163, photo=ChatPhoto(big_file_id='AQADBAAD9MExG4ESwVEACAMAAyP4TacBAAM_CLsItq2qHzYE', big_file_unique_id='AQAD9MExG4ESwVEB', small_file_id='AQADBAAD9MExG4ESwVEACAIAAyP4TacBAAM_CLsItq2qHzYE', small_file_unique_id='AQAD9MExG4ESwVEAAQ'), type=<ChatType.PRIVATE>, username='jang_cook_op')
2025-07-21 01:05:55,492 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:55,493 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:55,493 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:55,493 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,494 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:55,494 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:55,512 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:55,964 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:56 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'662'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:55,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:55,966 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,967 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:55,967 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:55,967 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:55,968 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Rahul_Yaah',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='𝗖꯭𝞂꯭𝗹𝞂͕֟፝𝗻꯭𝗲꯭𝗹', id=7907607255, photo=ChatPhoto(big_file_id='AQADBAAD1sIxG0mmwFMACAMAA9eCVNcBAAO9rg_Srtl3EjYE', big_file_unique_id='AQAD1sIxG0mmwFMB', small_file_id='AQADBAAD1sIxG0mmwFMACAIAA9eCVNcBAAO9rg_Srtl3EjYE', small_file_unique_id='AQAD1sIxG0mmwFMAAQ'), type=<ChatType.PRIVATE>, username='Rahul_Yaah')
2025-07-21 01:05:55,968 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:55,969 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:55,970 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:55,970 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:55,970 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:55,971 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:55,971 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:56,353 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:56 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'633'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:56,354 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:56,354 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:56,355 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:56,355 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:56,355 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:56,356 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('Ankit_Sarowar',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='𝗔꯭፝֟𝗻𝗸꯭𝗵𝗶꯭T', id=7866337744, photo=ChatPhoto(big_file_id='AQADBAAD3skxG2XqwVMACAMAA9DJ3tQBAANfEQmF7cm7UzYE', big_file_unique_id='AQAD3skxG2XqwVMB', small_file_id='AQADBAAD3skxG2XqwVMACAIAA9DJ3tQBAANfEQmF7cm7UzYE', small_file_unique_id='AQAD3skxG2XqwVMAAQ'), type=<ChatType.PRIVATE>, username='Ankit_Sarowar')
2025-07-21 01:05:56,356 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:56,357 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:56,357 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:56,357 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:56,358 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:56,358 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:56,359 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:56,757 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:57 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'541'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:56,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:56,758 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:56,759 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:56,759 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:56,759 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:56,760 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('aneesanand',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Anees', id=5794388425, photo=ChatPhoto(big_file_id='AQADBQAD6MIxG436-VYACAMAA8lZX1kBAANmX315GtQEiDYE', big_file_unique_id='AQAD6MIxG436-VYB', small_file_id='AQADBQAD6MIxG436-VYACAIAA8lZX1kBAANmX315GtQEiDYE', small_file_unique_id='AQAD6MIxG436-VYAAQ'), type=<ChatType.PRIVATE>, username='aneesanand')
2025-07-21 01:05:56,760 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:56,761 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:56,761 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:56,762 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:56,762 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:56,762 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:56,782 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:57,150 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:57 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'582'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:57,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:57,151 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:57,151 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:57,151 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:57,152 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:57,153 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('earning_grouphelp',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sonu', has_private_forwards=True, id=8079647641, photo=ChatPhoto(big_file_id='AQADBQADXcIxG20hEVQACAMAA5mjleEBAAOc4ZBDmdyumTYE', big_file_unique_id='AQADXcIxG20hEVQB', small_file_id='AQADBQADXcIxG20hEVQACAIAA5mjleEBAAOc4ZBDmdyumTYE', small_file_unique_id='AQADXcIxG20hEVQAAQ'), type=<ChatType.PRIVATE>, username='earning_grouphelp')
2025-07-21 01:05:57,153 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:57,154 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:57,155 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:57,155 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:57,156 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:57,156 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:57,157 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:57,561 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:58 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'310'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:57,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:57,563 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:57,563 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:57,564 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:57,564 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:57,565 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Alok_028',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Alok', id=6972925838, type=<ChatType.PRIVATE>, username='Alok_028')
2025-07-21 01:05:57,565 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:57,566 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:57,567 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:57,567 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:57,567 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:57,567 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:57,585 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:58,002 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:58 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'555'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:58,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:58,003 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,003 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:58,003 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:58,003 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:58,004 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Varun_predictions',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Varun', id=7957757500, photo=ChatPhoto(big_file_id='AQADBAAD1MExGw9BwVMACAMAAzy-UdoBAAO5PjDkWoBvlTYE', big_file_unique_id='AQAD1MExGw9BwVMB', small_file_id='AQADBAAD1MExGw9BwVMACAIAAzy-UdoBAAO5PjDkWoBvlTYE', small_file_unique_id='AQAD1MExGw9BwVMAAQ'), type=<ChatType.PRIVATE>, username='Varun_predictions')
2025-07-21 01:05:58,004 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:58,004 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,005 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:58,005 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,005 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:58,005 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,024 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:58,387 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:58 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'279'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:58,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:58,388 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,388 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:58,388 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:58,388 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:58,389 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Lucky', id=6398593372, last_name='Srinath', type=<ChatType.PRIVATE>)
2025-07-21 01:05:58,389 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:58,389 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,390 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:58,390 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,390 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:58,390 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,391 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:58,772 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:59 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'317'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:58,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:58,773 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,774 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:58,774 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:58,774 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:58,774 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Alokkkkk000',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='anmol', id=6770688041, type=<ChatType.PRIVATE>, username='Alokkkkk000')
2025-07-21 01:05:58,776 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:58,776 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,776 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:58,776 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:58,777 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:58,777 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:58,794 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:59,237 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:35:59 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'536'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:59,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:59,238 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:59,238 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:59,238 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:59,238 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:59,238 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('blazz007',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Apex', id=7361611975, photo=ChatPhoto(big_file_id='AQADBQADS8MxG-b78FQACAMAA8dIybYBAAMpGt35B5NSojYE', big_file_unique_id='AQADS8MxG-b78FQB', small_file_id='AQADBQADS8MxG-b78FQACAIAA8dIybYBAAMpGt35B5NSojYE', small_file_unique_id='AQADS8MxG-b78FQAAQ'), type=<ChatType.PRIVATE>, username='blazz007')
2025-07-21 01:05:59,238 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:59,239 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:59,239 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:59,239 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:59,240 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:59,240 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:59,257 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:05:59,672 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:00 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'998'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:05:59,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:05:59,673 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:05:59,673 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:05:59,673 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:05:59,673 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:05:59,674 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('MrIronMen',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='𝐌𝐑 𝐈 𝐑𝐎𝐍 𝐌𝐀𝐍 𝐅𝐑𝐎𝐌 𝐓𝐄𝐀𝐌 𝐌𝐑 😇𝐂𝐀𝐏𝐓𝐀𝐈𝐍', first_name='𝐌𝐑', id=6966105400, last_name='𝐈𝐑𝐎𝐍 𝐌𝐄𝐍', photo=ChatPhoto(big_file_id='AQADBAADXMIxG78bIVMACAMAAzhVNp8BAANRon2Q0UFk8TYE', big_file_unique_id='AQADXMIxG78bIVMB', small_file_id='AQADBAADXMIxG78bIVMACAIAAzhVNp8BAANRon2Q0UFk8TYE', small_file_unique_id='AQADXMIxG78bIVMAAQ'), type=<ChatType.PRIVATE>, username='MrIronMen')
2025-07-21 01:05:59,674 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:05:59,674 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:05:59,674 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:05:59,674 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:05:59,675 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:05:59,675 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:05:59,693 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:00,074 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:00 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'551'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:00,075 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:00,075 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,075 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:00,075 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:00,075 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:00,076 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('Music_love066',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Xxtention', id=7287475401, photo=ChatPhoto(big_file_id='AQADBAADUsIxG4a6sVEACAMAA8kMXrIBAANrO9wmLkavKTYE', big_file_unique_id='AQADUsIxG4a6sVEB', small_file_id='AQADBAADUsIxG4a6sVEACAIAA8kMXrIBAANrO9wmLkavKTYE', small_file_unique_id='AQADUsIxG4a6sVEAAQ'), type=<ChatType.PRIVATE>, username='Music_love066')
2025-07-21 01:06:00,076 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:00,076 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,076 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:00,076 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,077 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:00,077 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,094 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:00,452 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:01 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'271'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:00,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:00,453 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,453 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:00,453 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:00,453 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:00,453 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='SIM', id=7620947121, last_name='2', type=<ChatType.PRIVATE>)
2025-07-21 01:06:00,453 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:00,453 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,454 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:00,454 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,454 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:00,454 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,473 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:00,847 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:01 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'543'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:00,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:00,848 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:00,849 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('Spider_die',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Spidyyy', id=6798091513, photo=ChatPhoto(big_file_id='AQADBAAD3sExG2hzsFEACAMAA_mkMpUBAAOA0o6S18DXZjYE', big_file_unique_id='AQAD3sExG2hzsFEB', small_file_id='AQADBAAD3sExG2hzsFEACAIAA_mkMpUBAAOA0o6S18DXZjYE', small_file_unique_id='AQAD3sExG2hzsFEAAQ'), type=<ChatType.PRIVATE>, username='Spider_die')
2025-07-21 01:06:00,849 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:00,849 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:00,850 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:00,850 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:00,868 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:01,233 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:01 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'347'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:01,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:01,233 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:01,234 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:01,234 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:01,234 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:01,234 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Sivvu12',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Sʜɪᴠᴀ࿐♥︎', id=6359932009, type=<ChatType.PRIVATE>, username='Sivvu12')
2025-07-21 01:06:01,234 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:01,235 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:01,235 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:01,235 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:01,235 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:01,236 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:01,236 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:01,620 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:02 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'658'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:01,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:01,621 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:01,621 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:01,621 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:01,621 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:01,621 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Danish_fam',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='𝘿𝘼𝙉𝙄𝙎𝙃 🫶❤️ 𝙁𝙁', id=7425211273, photo=ChatPhoto(big_file_id='AQADBAADJcUxG-gTsFEACAMAA4m7k7oBAAM6cCHPMLho-DYE', big_file_unique_id='AQADJcUxG-gTsFEB', small_file_id='AQADBAADJcUxG-gTsFEACAIAA4m7k7oBAAM6cCHPMLho-DYE', small_file_unique_id='AQADJcUxG-gTsFEAAQ'), type=<ChatType.PRIVATE>, username='Danish_fam')
2025-07-21 01:06:01,622 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:01,622 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:01,622 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:01,622 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:01,623 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:01,623 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:01,640 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:02,007 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:02 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'667'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:02,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:02,009 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,009 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:02,009 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:02,009 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:02,010 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('HimanshuRaythor09',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Everyone Says Money Is Powerful So Make Money And Enjoy Your Life 🤑', first_name='Himanshu', id=6398473258, last_name='Raythor', photo=ChatPhoto(big_file_id='AQADBQADsMAxG825mFcACAMAAyr0YH0BAANJB-1LSRVrvTYE', big_file_unique_id='AQADsMAxG825mFcB', small_file_id='AQADBQADsMAxG825mFcACAIAAyr0YH0BAANJB-1LSRVrvTYE', small_file_unique_id='AQADsMAxG825mFcAAQ'), type=<ChatType.PRIVATE>, username='HimanshuRaythor09')
2025-07-21 01:06:02,010 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:02,011 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,011 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:02,012 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,012 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:02,013 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,030 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:02,392 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:02 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'334'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:02,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:02,393 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,393 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:02,393 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:02,394 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:02,394 - telegram.Bot - DEBUG - Chat(accent_color_id=3, active_usernames=('nareshuuuu',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Naresh', id=7505502343, last_name='123', type=<ChatType.PRIVATE>, username='nareshuuuu')
2025-07-21 01:06:02,394 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:02,394 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,396 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:02,396 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,397 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:02,397 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,417 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:02,804 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:03 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'545'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:02,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:02,805 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,805 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:02,805 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:02,805 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:02,806 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Olvie_music',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='OLIVIAA', id=7479365917, photo=ChatPhoto(big_file_id='AQADBAADW8QxGy480VEACAMAAx0Rzr0BAAOlWSh5IXtWmjYE', big_file_unique_id='AQADW8QxGy480VEB', small_file_id='AQADBAADW8QxGy480VEACAIAAx0Rzr0BAAOlWSh5IXtWmjYE', small_file_unique_id='AQADW8QxGy480VEAAQ'), type=<ChatType.PRIVATE>, username='Olvie_music')
2025-07-21 01:06:02,806 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:02,807 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,808 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:02,808 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:02,808 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:02,808 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:02,826 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:03,279 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:03 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'575'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:03,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:03,279 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:03,279 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:03,280 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:03,280 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:03,280 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('Drishtiseller',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Work 😐', first_name='Drishti', id=7459774606, photo=ChatPhoto(big_file_id='AQADBAADusExG-4Y0FEACAMAA44go7wBAAPwxrW-7Z2CMDYE', big_file_unique_id='AQADusExG-4Y0FEB', small_file_id='AQADBAADusExG-4Y0FEACAIAA44go7wBAAPwxrW-7Z2CMDYE', small_file_unique_id='AQADusExG-4Y0FEAAQ'), type=<ChatType.PRIVATE>, username='Drishtiseller')
2025-07-21 01:06:03,280 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:03,281 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:03,281 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:03,281 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:03,281 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:03,281 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:03,281 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:03,669 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:04 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'276'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:03,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:03,670 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:03,670 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:03,670 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:03,671 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:03,671 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='ROCKY', id=7021645183, last_name='STAR', type=<ChatType.PRIVATE>)
2025-07-21 01:06:03,671 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:03,671 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:03,672 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:03,672 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:03,672 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:03,672 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:03,672 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:04,083 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:04 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'569'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:04,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:04,084 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,084 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:04,084 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:04,085 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:04,085 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('ROYAL_earnerzz',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='ROYAL「 ⚡ 」', id=7450272260, photo=ChatPhoto(big_file_id='AQADBAADo8MxG9SV2FEACAMAAwQiErwBAAMNB0DGFfMxCzYE', big_file_unique_id='AQADo8MxG9SV2FEB', small_file_id='AQADBAADo8MxG9SV2FEACAIAAwQiErwBAAMNB0DGFfMxCzYE', small_file_unique_id='AQADo8MxG9SV2FEAAQ'), type=<ChatType.PRIVATE>, username='ROYAL_earnerzz')
2025-07-21 01:06:04,086 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:04,087 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,087 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:04,088 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,088 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:04,088 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,107 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:04,479 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'327'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:04,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:04,480 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,480 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:04,480 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:04,480 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:04,481 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Sivvva12',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Mahi', id=7510048215, last_name='15', type=<ChatType.PRIVATE>, username='Sivvva12')
2025-07-21 01:06:04,481 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:04,482 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,482 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:04,482 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,483 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:04,483 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,484 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:04,876 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'344'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:04,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:04,878 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,878 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:04,878 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:04,879 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:04,879 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('Dineshbawania',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Dinesh', id=6131950068, last_name='Bishnoi', type=<ChatType.PRIVATE>, username='Dineshbawania')
2025-07-21 01:06:04,879 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:04,880 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,881 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:04,881 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:04,882 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:04,882 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:04,901 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:05,265 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'305'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:05,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:05,266 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:05,266 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:05,266 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:05,266 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:05,266 - telegram.Bot - DEBUG - Chat(accent_color_id=4, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Shaik', has_private_forwards=True, id=7768262198, last_name='Shaik', type=<ChatType.PRIVATE>)
2025-07-21 01:06:05,266 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:05,267 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:05,267 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:05,267 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:05,267 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:05,267 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:05,286 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:05,654 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:06 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'397'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:05,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:05,654 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:05,655 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:05,655 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:05,655 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:05,655 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Shivuhhx4',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'birthdate': {'day': 4, 'month': 7, 'year': 2007}, 'max_reaction_count': 11}, bio='Jay shree ram 💓', first_name='@_shivuhhh', id=6164876537, type=<ChatType.PRIVATE>, username='Shivuhhx4')
2025-07-21 01:06:05,655 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:05,655 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:05,656 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:05,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:05,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:05,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:05,657 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:06,037 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:06 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'361'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:06,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:06,037 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,037 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:06,038 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:06,038 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:06,038 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('aftabalitna1',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='BadVibesForever', first_name='Aftab', id=5717535681, last_name='Ali', type=<ChatType.PRIVATE>, username='aftabalitna1')
2025-07-21 01:06:06,038 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:06,038 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,038 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:06,039 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,039 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:06,039 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,057 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:06,432 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:07 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'346'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:06,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:06,432 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,433 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:06,433 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:06,433 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:06,433 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Himanshugwala',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Himanshu', id=7504352200, last_name='Raythor', type=<ChatType.PRIVATE>, username='Himanshugwala')
2025-07-21 01:06:06,433 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:06,433 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,434 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:06,434 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,434 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:06,434 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,453 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:06,836 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:07 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'570'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:06,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:06,837 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,837 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:06,837 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:06,838 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:06,838 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Emperor00100',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Fire otp', id=5695363112, last_name='Emperor', photo=ChatPhoto(big_file_id='AQADBQADJsoxG0Jd4VYACAMAAyhYeFMBAAPBlZwByxAW6DYE', big_file_unique_id='AQADJsoxG0Jd4VYB', small_file_id='AQADBQADJsoxG0Jd4VYACAIAAyhYeFMBAAPBlZwByxAW6DYE', small_file_unique_id='AQADJsoxG0Jd4VYAAQ'), type=<ChatType.PRIVATE>, username='Emperor00100')
2025-07-21 01:06:06,838 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:06,838 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,839 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:06,839 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:06,839 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:06,839 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:06,858 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:07,218 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:07 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'376'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:07,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:07,219 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,219 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:07,220 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:07,220 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:07,220 - telegram.Bot - DEBUG - Chat(accent_color_id=4, active_usernames=('M_d_a_A_m_a_r',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Nothing', first_name='Amar Kumar Sahoo', has_private_forwards=True, id=1302284316, type=<ChatType.PRIVATE>, username='M_d_a_A_m_a_r')
2025-07-21 01:06:07,220 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:07,221 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:07,221 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:07,221 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,221 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:07,221 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:07,239 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:07,598 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:08 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'335'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:07,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:07,599 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,599 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:07,599 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:07,599 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:07,600 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('raj_974',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Raj', has_private_forwards=True, id=1757134930, type=<ChatType.PRIVATE>, username='raj_974')
2025-07-21 01:06:07,600 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:07,600 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:07,601 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:07,601 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,601 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:07,601 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:07,620 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:07,982 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:08 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'287'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:07,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:07,983 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,983 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:07,983 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:07,983 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:07,983 - telegram.Bot - DEBUG - Chat(accent_color_id=1, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Ravikaran', id=7830670800, last_name='Vishwakarma', type=<ChatType.PRIVATE>)
2025-07-21 01:06:07,983 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:07,984 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:07,984 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:07,984 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:07,984 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:07,985 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:08,003 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:08,381 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:08 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'343'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:08,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:08,382 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:08,382 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:08,382 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:08,382 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:08,382 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('e_w_p_admin',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='EWP', has_private_forwards=True, id=5089158828, type=<ChatType.PRIVATE>, username='e_w_p_admin')
2025-07-21 01:06:08,382 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:08,383 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:08,383 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:08,383 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:08,384 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:08,384 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:08,402 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:08,764 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:09 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'330'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:08,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:08,766 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:08,766 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:08,766 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:08,767 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:08,767 - telegram.Bot - DEBUG - Chat(accent_color_id=0, active_usernames=('Ravi_1516',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, bio='Ravik1992', first_name='Ravi', id=7870284688, type=<ChatType.PRIVATE>, username='Ravi_1516')
2025-07-21 01:06:08,767 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:08,768 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:08,769 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:08,769 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:08,769 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:08,769 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:08,787 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:09,152 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:09 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'373'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:09,153 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:09,155 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,156 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:09,156 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:09,156 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:09,157 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Chavvi1200',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='💞 छवि', id=7857702165, last_name='Jain 💞', type=<ChatType.PRIVATE>, username='Chavvi1200')
2025-07-21 01:06:09,158 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:09,160 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,162 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:09,162 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,164 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:09,164 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,184 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:09,546 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:10 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'277'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:09,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:09,547 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,547 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:09,547 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:09,547 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:09,548 - telegram.Bot - DEBUG - Chat(accent_color_id=3, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Umang', id=6276709176, last_name='Saini', type=<ChatType.PRIVATE>)
2025-07-21 01:06:09,548 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:09,548 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,549 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:09,549 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,550 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:09,550 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,568 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:09,938 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:10 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'1304'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:09,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:09,939 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,939 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:09,940 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:09,940 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:09,940 - telegram.Bot - DEBUG - Chat(accent_color_id=6, active_usernames=('TechVibeOwner',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002664454121, 'title': 'ALL PAYMENTS 𝗧𝗥𝗔𝗡𝗦𝗙𝗘𝗥 💸 𝙎𝘾𝙍𝙀𝙀𝙉 𝙕𝙊𝙉𝙀™TRUSTED', 'username': 'techvibeNETPROFE', 'type': 'channel'}, 'max_reaction_count': 11}, bio='⏤͟͟͞͞ 𖣘 Hᴇʏ Dᴇᴀʀ 🇮🇳 ⛦ I Aᴍ Aᴜᴛᴏ Pᴏꜱᴛᴇʀ Rᴏʙᴏᴛ 🜲 ‼️Mᴇssᴀɢᴇ Mᴇ', first_name='🔮 TᴇᴄʜVɪʙᴇ 𝙉𝙚𝙩™', has_private_forwards=True, id=6840653084, last_name='Owner', photo=ChatPhoto(big_file_id='AQADBQADdNQxG34E-VYACAMAAxwVvJcBAANeVDqTEg7xMTYE', big_file_unique_id='AQADdNQxG34E-VYB', small_file_id='AQADBQADdNQxG34E-VYACAIAAxwVvJcBAANeVDqTEg7xMTYE', small_file_unique_id='AQADdNQxG34E-VYAAQ'), type=<ChatType.PRIVATE>, username='TechVibeOwner')
2025-07-21 01:06:09,940 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:09,941 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,942 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:09,942 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:09,942 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:09,942 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:09,960 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:10,329 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:10 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'569'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:10,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:10,330 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:10,330 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:10,330 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:10,330 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:10,330 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('Emperor001000',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='天', id=7131134915, last_name='皇', photo=ChatPhoto(big_file_id='AQADBQADzMMxGwQSOVUACAMAA8N7DKkBAAPWRIFoYvOcZjYE', big_file_unique_id='AQADzMMxGwQSOVUB', small_file_id='AQADBQADzMMxGwQSOVUACAIAA8N7DKkBAAPWRIFoYvOcZjYE', small_file_unique_id='AQADzMMxGwQSOVUAAQ'), type=<ChatType.PRIVATE>, username='Emperor001000')
2025-07-21 01:06:10,331 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:10,331 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:10,331 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:10,331 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:10,331 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:10,332 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:10,350 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:10,719 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:11 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'315'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:10,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:10,719 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:10,719 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:10,719 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:10,719 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:10,719 - telegram.Bot - DEBUG - Chat(accent_color_id=1, active_usernames=('Aviiii1993',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Rohit', id=7470594111, type=<ChatType.PRIVATE>, username='Aviiii1993')
2025-07-21 01:06:10,721 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:10,721 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:10,721 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:10,721 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:10,721 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:10,721 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:10,739 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:11,109 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:11 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'483'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:11,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:11,110 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:11,110 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:11,110 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:11,110 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:11,110 - telegram.Bot - DEBUG - Chat(accent_color_id=0, api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='Adish', id=6266612009, photo=ChatPhoto(big_file_id='AQADBQADnrUxGyXt4FcACAMAAynphHUBAANqiIBuKeNpJDYE', big_file_unique_id='AQADnrUxGyXt4FcB', small_file_id='AQADBQADnrUxGyXt4FcACAIAAynphHUBAANqiIBuKeNpJDYE', small_file_unique_id='AQADnrUxGyXt4FcAAQ'), type=<ChatType.PRIVATE>)
2025-07-21 01:06:11,111 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:11,111 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:11,111 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:11,112 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:11,112 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:11,112 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:11,131 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:11,486 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:12 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'571'), (b'Connection', b'keep-alive'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:11,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:11,487 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:11,487 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:11,487 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:11,487 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:11,488 - telegram.Bot - DEBUG - Chat(accent_color_id=5, active_usernames=('Emperor0000100',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'max_reaction_count': 11}, first_name='天', id=7599717473, last_name='皇', photo=ChatPhoto(big_file_id='AQADBQADfMMxG1AMsVUACAMAA2F8-sQBAAMEjI-cH1zy5TYE', big_file_unique_id='AQADfMMxG1AMsVUB', small_file_id='AQADBQADfMMxG1AMsVUACAIAA2F8-sQBAAMEjI-cH1zy5TYE', small_file_unique_id='AQADfMMxG1AMsVUAAQ'), type=<ChatType.PRIVATE>, username='Emperor0000100')
2025-07-21 01:06:11,488 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:11,488 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:11,489 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:11,489 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:11,489 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:11,489 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-21 01:06:11,506 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:11,880 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Server', b'nginx/1.18.0'), (b'Date', b'Sun, 20 Jul 2025 19:36:12 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'889'), (b'Connection', b'close'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'), (b'Access-Control-Expose-Headers', b'Content-Length,Content-Type,Date,Server,Connection')])
2025-07-21 01:06:11,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8152909781:AAHLtc-tzefSP86ZvjBYNLv64NEZFKvBaZM/getChat "HTTP/1.1 200 OK"
2025-07-21 01:06:11,880 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-21 01:06:11,880 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-21 01:06:11,880 - httpcore.http11 - DEBUG - response_closed.started
2025-07-21 01:06:11,881 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-21 01:06:11,881 - telegram.Bot - DEBUG - Chat(accent_color_id=2, active_usernames=('FREEFIRE_TU',), api_kwargs={'can_send_gift': True, 'accepted_gift_types': {'unlimited_gifts': True, 'limited_gifts': True, 'unique_gifts': True, 'premium_subscription': True}, 'personal_chat': {'id': -1002396929800, 'title': '『 𝙁𝙁 𝙏𝙤𝙪𝙧𝙣𝙖𝙢𝙚𝙣𝙩™』', 'username': 'TechVibeFF', 'type': 'channel'}, 'max_reaction_count': 11}, first_name='╰‿╯ 『』『TURNAMENT』『FREE FIRE』ʙᴏʏツ', id=7006020429, photo=ChatPhoto(big_file_id='AQADBQADxsgxG7H9sVcACAMAA01jl6EBAAPjaFovyG0l3jYE', big_file_unique_id='AQADxsgxG7H9sVcB', small_file_id='AQADBQADxsgxG7H9sVcACAIAA01jl6EBAAPjaFovyG0l3jYE', small_file_unique_id='AQADxsgxG7H9sVcAAQ'), type=<ChatType.PRIVATE>, username='FREEFIRE_TU')
2025-07-21 01:06:11,881 - telegram.Bot - DEBUG - Exiting: get_chat
2025-07-21 01:06:11,881 - httpcore.connection - DEBUG - connect_tcp.started host='api.telegram.org' port=443 local_address=None timeout=5.0 socket_options=None
2025-07-21 01:06:11,899 - telegram.Bot - DEBUG - Entering: get_chat
2025-07-21 01:06:12,084 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001D9E4A38EC0>
2025-07-21 01:06:12,085 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001D9E4837250> server_hostname='api.telegram.org' timeout=5.0
2025-07-21 01:06:12,291 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001D9E4A38560>
2025-07-21 01:06:12,291 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-21 01:06:12,291 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-21 01:06:12,292 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-21 01:06:12,292 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-21 01:06:12,292 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
