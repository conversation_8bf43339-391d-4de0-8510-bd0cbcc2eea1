2025-07-21 16:26:16,262 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:26:18,343 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:18,344 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:21,106 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,173 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,174 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:27,077 - services.user_service - INFO - Created new user: ********** (Getha)
2025-07-21 16:26:28,478 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:26:30,267 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Getha (ID: **********): success
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:48,802 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:59,397 - services.user_service - INFO - Created new user: 8135061794 (Someshhh)
2025-07-21 16:27:00,564 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:01,883 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Someshhh (ID: 8135061794): success
2025-07-21 16:27:12,254 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Getha
2025-07-21 16:27:39,740 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Someshhh
2025-07-21 16:27:40,568 - services.user_service - INFO - Created new user: ********** (Goat)
2025-07-21 16:27:41,848 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:43,426 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Goat (ID: **********): success
2025-07-21 16:28:42,357 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:28:45,850 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1d517104627ee2037073'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:28:48,852 - services.promotion_report_service - INFO - User ********** has 274 referrals, using optimized calculation
2025-07-21 16:28:54,619 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:01,835 - handlers.callback_handlers - ERROR - Error in _handle_redeem_gift_code: Message to edit not found
2025-07-21 16:29:03,431 - services.user_service - INFO - Created new user: 1963388963 (Sudip)
2025-07-21 16:29:04,582 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:29:05,865 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sudip (ID: 1963388963): success
2025-07-21 16:29:19,356 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095559.196fdefa
2025-07-21 16:29:25,699 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:29,919 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Sudip
2025-07-21 16:29:55,103 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:29:55,104 - __main__ - INFO - Shutting down bot...
2025-07-21 16:29:55,791 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:29:55,835 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:29:55,836 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:30:02,106 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:30:15,085 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:30:16,750 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:30:16,751 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:30:17,163 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:30:18,103 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:30:18,155 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:30:18,155 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:30:18,156 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:30:18,156 - __main__ - INFO - Starting bot polling...
2025-07-21 16:30:18,366 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:30:18,579 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:25,189 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:28,867 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:30,629 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:32,453 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:43,246 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-21 16:30:43,248 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1dd87104627ee2037076'), 'user_id': **********, 'created_at': 1753095640, 'data': {}, 'step': 'user_details_get_id', 'updated_at': 1753095640}
2025-07-21 16:30:43,249 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:30:43,250 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:30:43,252 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 16:31:29,101 - services.user_service - INFO - Created new user: 7073225065 (Kenneth)
2025-07-21 16:31:30,296 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:31,663 - handlers.user_handlers - INFO - Notification sent to referrer 5862992413 about new user Kenneth (ID: 7073225065): success
2025-07-21 16:31:33,466 - services.user_service - INFO - Created new user: 5274518837 (Sangram)
2025-07-21 16:31:34,814 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:36,147 - handlers.user_handlers - INFO - Notification sent to referrer 7894803745 about new user Sangram (ID: 5274518837): success
2025-07-21 16:31:51,265 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 16:31:52,066 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-21 16:31:52,477 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 16:31:52,524 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-21 16:31:52,625 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 16:31:52,683 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:54,545 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:58,821 - services.referral_service - INFO - Referral reward processed: 5862992413 earned ₹3 for referring Kenneth
2025-07-21 16:32:05,472 - services.referral_service - INFO - Referral reward processed: 7894803745 earned ₹2 for referring Sangram
2025-07-21 16:32:08,633 - services.user_service - INFO - Created new user: 2069827731 (Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS)
2025-07-21 16:32:10,122 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:11,470 - handlers.user_handlers - INFO - Notification sent to referrer 7589583468 about new user Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS (ID: 2069827731): success
2025-07-21 16:32:21,050 - services.user_service - INFO - Created new user: ********** (Randeep)
2025-07-21 16:32:22,390 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:23,698 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Randeep (ID: **********): success
2025-07-21 16:32:26,652 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:32:32,708 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:33:22,106 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095802.fe75e18b
2025-07-21 16:33:30,631 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:33:40,194 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:34:10,330 - services.user_service - INFO - Created new user: 6307558097 (Vishal)
2025-07-21 16:34:11,555 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:34:12,965 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Vishal (ID: 6307558097): success
2025-07-21 16:34:25,573 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 16:34:25,788 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 16:34:42,936 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:34:56,679 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹1 for referring Randeep
2025-07-21 16:35:04,019 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹3 for referring Vishal
2025-07-21 16:35:22,460 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Goat
2025-07-21 16:35:36,480 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:35:36,480 - __main__ - INFO - Shutting down bot...
2025-07-21 16:35:37,108 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:35:37,154 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:35:37,155 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:35:54,105 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:36:07,056 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:36:08,811 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:36:08,812 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:36:11,915 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:36:12,986 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:36:13,032 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:36:13,042 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:36:13,043 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:36:13,056 - __main__ - INFO - Starting bot polling...
2025-07-21 16:36:13,274 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:36:13,487 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:36:14,456 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:36:16,954 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:17,326 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:17,720 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:18,549 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:18,550 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:18,919 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,093 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:19,467 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,841 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,842 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,843 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:19,844 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:20,591 - handlers.user_handlers - ERROR - Failed to edit error message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:20,592 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:20,961 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:20,962 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:21,322 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:21,324 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:21,792 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,172 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:22,173 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:22,543 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,545 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:22,920 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,290 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,291 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,292 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:23,292 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,067 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,838 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:25,584 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:27,571 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:29,761 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:31,213 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:32,599 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 16:36:33,968 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 16:36:35,492 - services.promotion_report_service - INFO - User ********** has 51 referrals, using optimized calculation
2025-07-21 16:36:35,890 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:37,860 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:39,132 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:36:40,401 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:43,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:43,757 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:43,758 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:44,132 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:44,581 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: joined
2025-07-21 16:36:44,951 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:45,574 - services.user_service - INFO - Created new user: ********** (Rintu)
2025-07-21 16:36:48,616 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Rintu (ID: **********): success
2025-07-21 16:36:50,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:51,955 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:53,486 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:55,012 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:55,390 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:59,280 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:37:00,827 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:02,955 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:03,326 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:05,284 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: extraRewards
2025-07-21 16:37:09,477 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_approve_withdrawal_**********
2025-07-21 16:37:12,689 - services.withdrawal_service - INFO - Withdrawal approved: User **********, Amount ₹100
2025-07-21 16:37:13,499 - handlers.admin_handlers - INFO - Admin ********** (Kêviñ) approved withdrawal for user ********** (₹100)
2025-07-21 16:37:13,816 - services.user_service - INFO - Created new user: 6936038369 (S)
2025-07-21 16:37:16,661 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user S (ID: 6936038369): success
2025-07-21 16:37:18,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:22,452 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:35,323 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:37,124 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:38,450 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:38,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:40,726 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:37:44,246 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:53,028 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:57,636 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:59,033 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:38:02,624 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:38:05,230 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:38:06,426 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:38:09,021 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1f957104627ee203707b'), 'user_id': **********, 'created_at': 1753096085, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753096085}
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message text: 'https://t.me/InstantoPayBot?start=**********
I've Got Up To ₹100! Click URL To Join'
2025-07-21 16:38:37,729 - services.user_service - INFO - Created new user: ********** (4meen)
2025-07-21 16:38:40,437 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 4meen (ID: **********): success
2025-07-21 16:38:57,463 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:39:01,313 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:10,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:39:12,140 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:17,003 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:39:18,373 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:47,661 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:52,569 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:59,812 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:40:08,825 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:40:10,402 - services.user_service - INFO - Created new user: ********** (Vishnu Vardhan)
2025-07-21 16:40:13,361 - handlers.user_handlers - INFO - Notification sent to referrer 5862992413 about new user Vishnu Vardhan (ID: **********): success
2025-07-21 16:40:46,436 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:14,651 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:17,209 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:20,904 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring 4meen
2025-07-21 16:41:21,461 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:41:29,981 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:40,574 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:41:50,552 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:42:02,986 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:04,703 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:42:12,345 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:42:14,714 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:42:20,922 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:26,961 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:42:36,510 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:42:47,097 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:42:49,869 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
