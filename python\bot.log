2025-07-21 16:26:16,262 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:26:18,343 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:18,344 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:21,106 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,173 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,174 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:27,077 - services.user_service - INFO - Created new user: ********** (Getha)
2025-07-21 16:26:28,478 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:26:30,267 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Getha (ID: **********): success
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:48,802 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:59,397 - services.user_service - INFO - Created new user: 8135061794 (Someshhh)
2025-07-21 16:27:00,564 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:01,883 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Someshhh (ID: 8135061794): success
2025-07-21 16:27:12,254 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Getha
2025-07-21 16:27:39,740 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Someshhh
2025-07-21 16:27:40,568 - services.user_service - INFO - Created new user: ********** (Goat)
2025-07-21 16:27:41,848 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:43,426 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Goat (ID: **********): success
2025-07-21 16:28:42,357 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:28:45,850 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1d517104627ee2037073'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:28:48,852 - services.promotion_report_service - INFO - User ********** has 274 referrals, using optimized calculation
2025-07-21 16:28:54,619 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:01,835 - handlers.callback_handlers - ERROR - Error in _handle_redeem_gift_code: Message to edit not found
2025-07-21 16:29:03,431 - services.user_service - INFO - Created new user: 1963388963 (Sudip)
2025-07-21 16:29:04,582 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:29:05,865 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sudip (ID: 1963388963): success
2025-07-21 16:29:19,356 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095559.196fdefa
2025-07-21 16:29:25,699 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:29,919 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Sudip
2025-07-21 16:29:55,103 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:29:55,104 - __main__ - INFO - Shutting down bot...
2025-07-21 16:29:55,791 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:29:55,835 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:29:55,836 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:30:02,106 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:30:15,085 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:30:16,750 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:30:16,751 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:30:17,163 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:30:18,103 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:30:18,155 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:30:18,155 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:30:18,156 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:30:18,156 - __main__ - INFO - Starting bot polling...
2025-07-21 16:30:18,366 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:30:18,579 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:25,189 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:28,867 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:30,629 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:32,453 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:43,246 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-21 16:30:43,248 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1dd87104627ee2037076'), 'user_id': **********, 'created_at': 1753095640, 'data': {}, 'step': 'user_details_get_id', 'updated_at': 1753095640}
2025-07-21 16:30:43,249 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:30:43,250 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:30:43,252 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 16:31:29,101 - services.user_service - INFO - Created new user: 7073225065 (Kenneth)
2025-07-21 16:31:30,296 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:31,663 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Kenneth (ID: 7073225065): success
2025-07-21 16:31:33,466 - services.user_service - INFO - Created new user: 5274518837 (Sangram)
2025-07-21 16:31:34,814 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:36,147 - handlers.user_handlers - INFO - Notification sent to referrer 7894803745 about new user Sangram (ID: 5274518837): success
2025-07-21 16:31:51,265 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 16:31:52,066 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-21 16:31:52,477 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 16:31:52,524 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-21 16:31:52,625 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 16:31:52,683 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:54,545 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:58,821 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Kenneth
2025-07-21 16:32:05,472 - services.referral_service - INFO - Referral reward processed: 7894803745 earned ₹2 for referring Sangram
2025-07-21 16:32:08,633 - services.user_service - INFO - Created new user: 2069827731 (Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS)
2025-07-21 16:32:10,122 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:11,470 - handlers.user_handlers - INFO - Notification sent to referrer 7589583468 about new user Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS (ID: 2069827731): success
2025-07-21 16:32:21,050 - services.user_service - INFO - Created new user: ********** (Randeep)
2025-07-21 16:32:22,390 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:23,698 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Randeep (ID: **********): success
2025-07-21 16:32:26,652 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:32:32,708 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:33:22,106 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095802.fe75e18b
2025-07-21 16:33:30,631 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:33:40,194 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:34:10,330 - services.user_service - INFO - Created new user: 6307558097 (Vishal)
2025-07-21 16:34:11,555 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:34:12,965 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Vishal (ID: 6307558097): success
2025-07-21 16:34:25,573 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 16:34:25,788 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 16:34:42,936 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:34:56,679 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹1 for referring Randeep
2025-07-21 16:35:04,019 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹3 for referring Vishal
2025-07-21 16:35:22,460 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Goat
2025-07-21 16:35:36,480 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:35:36,480 - __main__ - INFO - Shutting down bot...
2025-07-21 16:35:37,108 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:35:37,154 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:35:37,155 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:35:54,105 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:36:07,056 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:36:08,811 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:36:08,812 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:36:11,915 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:36:12,986 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:36:13,032 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:36:13,042 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:36:13,043 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:36:13,056 - __main__ - INFO - Starting bot polling...
2025-07-21 16:36:13,274 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:36:13,487 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:36:14,456 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:36:16,954 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:17,326 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:17,720 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:18,549 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:18,550 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:18,919 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,093 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:19,467 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,841 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,842 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,843 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:19,844 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:20,591 - handlers.user_handlers - ERROR - Failed to edit error message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:20,592 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:20,961 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:20,962 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:21,322 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:21,324 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:21,792 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,172 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:22,173 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:22,543 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,545 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:22,920 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,290 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,291 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,292 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:23,292 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,067 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,838 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:25,584 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:27,571 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:29,761 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:31,213 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:32,599 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 16:36:33,968 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 16:36:35,492 - services.promotion_report_service - INFO - User ********** has 51 referrals, using optimized calculation
2025-07-21 16:36:35,890 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:37,860 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:39,132 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:36:40,401 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:43,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:43,757 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:43,758 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:44,132 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:44,581 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: joined
2025-07-21 16:36:44,951 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:45,574 - services.user_service - INFO - Created new user: ********** (Rintu)
2025-07-21 16:36:48,616 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Rintu (ID: **********): success
2025-07-21 16:36:50,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:51,955 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:53,486 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:55,012 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:55,390 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:59,280 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:37:00,827 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:02,955 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:03,326 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:05,284 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: extraRewards
2025-07-21 16:37:09,477 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_approve_withdrawal_**********
2025-07-21 16:37:12,689 - services.withdrawal_service - INFO - Withdrawal approved: User **********, Amount ₹100
2025-07-21 16:37:13,499 - handlers.admin_handlers - INFO - Admin ********** (Kêviñ) approved withdrawal for user ********** (₹100)
2025-07-21 16:37:13,816 - services.user_service - INFO - Created new user: ********** (S)
2025-07-21 16:37:16,661 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user S (ID: **********): success
2025-07-21 16:37:18,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:22,452 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:35,323 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:37,124 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:38,450 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:38,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:40,726 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:37:44,246 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:53,028 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:57,636 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:59,033 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:38:02,624 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:38:05,230 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:38:06,426 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:38:09,021 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1f957104627ee203707b'), 'user_id': **********, 'created_at': 1753096085, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753096085}
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message text: 'https://t.me/InstantoPayBot?start=**********
I've Got Up To ₹100! Click URL To Join'
2025-07-21 16:38:37,729 - services.user_service - INFO - Created new user: ********** (4meen)
2025-07-21 16:38:40,437 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 4meen (ID: **********): success
2025-07-21 16:38:57,463 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:39:01,313 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:10,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:39:12,140 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:17,003 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:39:18,373 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:47,661 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:52,569 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:59,812 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:40:08,825 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:40:10,402 - services.user_service - INFO - Created new user: ********** (Vishnu Vardhan)
2025-07-21 16:40:13,361 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Vishnu Vardhan (ID: **********): success
2025-07-21 16:40:46,436 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:14,651 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:17,209 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:20,904 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring 4meen
2025-07-21 16:41:21,461 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:41:29,981 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:40,574 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:41:50,552 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:42:02,986 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:04,703 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:42:12,345 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:42:14,714 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:42:20,922 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:26,961 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:42:36,510 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:42:47,097 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:42:49,869 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:43:27,714 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:43:40,646 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:43:42,273 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:43:42,273 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:43:42,713 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:43:43,642 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:43:43,686 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:43:43,686 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:43:43,686 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:43:43,686 - __main__ - INFO - Starting bot polling...
2025-07-21 16:43:43,902 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:43:44,118 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:43:51,568 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:44:01,252 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:44:09,431 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:44:12,931 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:44:21,404 - services.user_service - INFO - Created new user: 1253475096 (Prakash)
2025-07-21 16:44:24,458 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Prakash (ID: 1253475096): success
2025-07-21 16:44:26,925 - services.user_service - INFO - Created new user: ********** (Ranjitha)
2025-07-21 16:44:29,832 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ranjitha (ID: **********): success
2025-07-21 16:45:13,462 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:45:14,746 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:45:14,792 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:45:14,793 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:45:14,793 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:45:15,012 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:15,227 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:15,266 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:45:15,660 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:45:19,884 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Ranjitha
2025-07-21 16:45:22,610 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:45:25,454 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:45:26,217 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:45:26,265 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:45:26,265 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:45:26,266 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:45:26,479 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:26,689 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:26,694 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:45:32,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:45:38,548 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:45:44,806 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:45:55,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 16:45:57,512 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:46:00,707 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:46:02,472 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:46:03,256 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:46:03,300 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: False
2025-07-21 16:46:03,300 - handlers.admin_handlers - INFO - Creating keyboard with 3 rows
2025-07-21 16:46:03,301 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:46:03,681 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:46:07,575 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:46:09,335 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:46:13,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:46:15,678 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:46:17,802 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:46:21,351 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹2 for referring S
2025-07-21 16:46:33,965 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:46:46,876 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e216d7104627ee203707d'), 'user_id': **********, 'created_at': 1753096558, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753096558}
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:46:53,515 - services.task_service - INFO - Created new task submission: submission_1753096613.ac184a7d
2025-07-21 16:47:03,258 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:47:05,014 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:47:10,112 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:47:12,200 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:47:12,971 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:47:13,012 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:47:13,013 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:47:13,013 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:47:13,220 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:13,429 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:13,431 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:47:17,587 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:47:21,459 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:47:22,223 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:47:22,267 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:47:22,267 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:47:22,268 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:47:22,479 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:22,686 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:22,689 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:47:24,790 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:47:26,062 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:47:35,052 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 16:47:39,995 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:47:58,494 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:48:56,478 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:49:02,283 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e21d07104627ee203707e'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message text: 'AC/NO -***********
IFSC CODE-MAHB0000437
NAME- PRAFUL DEVRAO SORTE'
2025-07-21 16:49:11,777 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 16:49:35,619 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e21d07104627ee203707e'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message text: '***********'
2025-07-21 16:49:36,072 - services.withdrawal_service - INFO - Duplicate check for account ***********: found 0 matches
2025-07-21 16:49:36,871 - services.withdrawal_service - INFO - Duplicate check for account ***********: found 0 matches
2025-07-21 16:49:42,934 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:49:59,732 - handlers.callback_handlers - INFO - Processing callback query from user **********: set name
2025-07-21 16:50:18,646 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-21 16:50:18,648 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22607104627ee203707f'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-21 16:50:18,648 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message text: 'Praful sorte'
2025-07-21 16:50:26,315 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:50:32,114 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 16:50:41,644 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 16:50:41,644 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22807104627ee2037080'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:50:41,646 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:50:41,646 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 16:50:42,146 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: **********
2025-07-21 16:50:42,190 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 16:50:42,192 - services.withdrawal_service - ERROR - Invalid mobile number format: ********** (missing country code)
2025-07-21 16:50:42,192 - handlers.session_handlers - ERROR - Failed to send OTP to ********** for user **********
2025-07-21 16:50:43,385 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:50:53,010 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:51:02,979 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 16:51:23,976 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 16:51:23,976 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22a07104627ee2037081'), 'user_id': **********, 'created_at': 1753096864, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753096864}
2025-07-21 16:51:23,977 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:51:23,978 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:51:23,978 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:51:23,979 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:51:23,979 - handlers.session_handlers - INFO - Message text: '+91**********'
2025-07-21 16:51:25,195 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +91**********
2025-07-21 16:51:25,244 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 16:51:25,244 - services.withdrawal_service - INFO - Converted +91********** to API format: **********
2025-07-21 16:51:25,245 - services.withdrawal_service - INFO - Sending OTP to +91********** (API format: **********) using URL: https://sms.renflair.in/V1.php
2025-07-21 16:51:27,829 - services.withdrawal_service - INFO - OTP API response for +91**********: Status 200, Content: {"return":true,"request_id":"f9Nlw0BujsMZzgh","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - OTP API analysis for +91********** (API format: **********): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - OTP API call successful for +91**********. OTP: 2039
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-21 16:51:28,833 - handlers.session_handlers - INFO - OTP API call successful for +91**********, user ********** moved to verification step
2025-07-21 16:51:37,203 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:51:37,203 - __main__ - INFO - Shutting down bot...
2025-07-21 16:51:37,849 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:51:37,892 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:51:37,892 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:51:43,566 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:51:56,559 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:51:58,171 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:51:58,172 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:51:58,686 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:51:59,577 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:51:59,618 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:51:59,618 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:51:59,618 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:51:59,619 - __main__ - INFO - Starting bot polling...
2025-07-21 16:51:59,825 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:52:00,032 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:52:00,747 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:52:02,690 - services.user_service - INFO - Created new user: ********** (Sham)
2025-07-21 16:52:05,506 - handlers.user_handlers - INFO - Notification sent to referrer 7802823735 about new user Sham (ID: **********): success
2025-07-21 16:52:07,131 - handlers.callback_handlers - INFO - Processing callback query from user **********: gift_broadcast
2025-07-21 16:52:07,516 - handlers.callback_handlers - WARNING - Ignoring expired query 'gift_broadcast' from user **********
2025-07-21 16:52:07,776 - services.user_service - INFO - Created new user: ********** (Ayush)
2025-07-21 16:52:10,741 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayush (ID: **********): success
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22a07104627ee2037081'), 'user_id': **********, 'created_at': 1753096887, 'data': {'mobile_number': '+91**********', 'otp': '2039', 'otp_timestamp': 1753096887}, 'step': 'verify_otp', 'updated_at': 1753096887}
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message text: '2039'
2025-07-21 16:52:13,975 - handlers.callback_handlers - INFO - Processing callback query from user **********: gift_broadcast
2025-07-21 16:52:17,206 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:52:20,121 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:52:33,697 - handlers.callback_handlers - INFO - Processing callback query from user **********: set ifsc
2025-07-21 16:52:35,136 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:52:35,902 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:52:35,946 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: False
2025-07-21 16:52:35,947 - handlers.admin_handlers - INFO - Creating keyboard with 3 rows
2025-07-21 16:52:35,947 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:52:36,350 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:52:38,043 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_text
2025-07-21 16:52:40,413 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_text ===
2025-07-21 16:52:40,414 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22fe7104627ee2037084'), 'user_id': **********, 'created_at': 1753096959, 'data': {}, 'step': 'broadcast_text', 'updated_at': 1753096959}
2025-07-21 16:52:40,415 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:40,415 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message text: 'jkjhk'
2025-07-21 16:52:40,461 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Data to save: {'text': 'jkjhk'}
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:52:40,512 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:52:40,512 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Matched count: 0
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Modified count: 0
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Upserted ID: 687e22ff7104627ee2037085
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:52:41,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:52:45,579 - services.referral_service - INFO - Referral reward processed: 7802823735 earned ₹5 for referring Sham
2025-07-21 16:52:46,128 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:52:46,883 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:52:46,928 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:52:46,929 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:52:46,929 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:52:47,325 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:52:50,422 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_buttons
2025-07-21 16:52:51,664 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22fa7104627ee2037083'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message text: 'MAHB0000437'
2025-07-21 16:52:53,159 - utils.helpers - INFO - Cached bank details for IFSC: MAHB0000437
2025-07-21 16:52:59,658 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-21 16:52:59,659 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e230a7104627ee2037086'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': **********}
2025-07-21 16:52:59,659 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message text: 'Visit Website | https://example.com
Join Channel | https://t.me/channel'
2025-07-21 16:52:59,700 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:52:59,701 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096960, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]}
2025-07-21 16:52:59,701 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:52:59,702 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:52:59,749 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:53:01,696 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:53:02,490 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:02,951 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:02,952 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:53:06,751 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:53:19,274 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:53:23,120 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Ayush
2025-07-21 16:53:23,672 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_MAHB0000437
2025-07-21 16:53:25,606 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:53:27,544 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_MAHB0000437
2025-07-21 16:53:28,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:53:29,110 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:29,151 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:29,542 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:31,884 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_media
2025-07-21 16:53:44,433 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:53:46,491 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_media ===
2025-07-21 16:53:46,491 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23347104627ee2037087'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_media', 'updated_at': **********}
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - === ROUTING TO BROADCAST MEDIA HANDLER for user ********** ===
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING START for user ********** ===
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message object exists: True
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message ID: 1136373
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message text: None
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message caption: None
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - === CHECKING MEDIA TYPES for user ********** ===
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has audio: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has voice: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has sticker: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has animation: False
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - Message has video_note: False
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - ✅ PHOTO detected - file_id: AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - === MEDIA PROCESSING SUCCESS for user ********** ===
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - Media data created: {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - === SAVING TO BROADCAST DRAFT for user ********** ===
2025-07-21 16:53:46,497 - handlers.session_handlers - INFO - Getting existing broadcast draft for user **********
2025-07-21 16:53:46,541 - handlers.session_handlers - INFO - Existing draft: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]}
2025-07-21 16:53:46,542 - handlers.session_handlers - INFO - Updated draft with media: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]], 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 16:53:46,542 - handlers.session_handlers - INFO - Calling save_broadcast_draft for user **********
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]], 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:53:46,592 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:53:46,594 - handlers.session_handlers - INFO - === BROADCAST DRAFT SAVE RESULT for user **********: True ===
2025-07-21 16:53:46,594 - handlers.session_handlers - INFO - Clearing session for user **********
2025-07-21 16:53:46,641 - handlers.session_handlers - INFO - Session cleared for user **********
2025-07-21 16:53:46,642 - handlers.session_handlers - INFO - === SENDING SUCCESS MESSAGE to user ********** ===
2025-07-21 16:53:47,050 - handlers.session_handlers - INFO - ✅ SUCCESS MESSAGE SENT to user **********
2025-07-21 16:53:47,051 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING END for user ********** ===
2025-07-21 16:53:48,770 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:53:49,522 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:49,567 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:49,567 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:49,568 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:53:49,569 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:49,972 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:51,599 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:53:56,239 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:54:11,211 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:54:13,180 - services.user_service - INFO - Created new user: ********** (Fuhad Ak)
2025-07-21 16:54:16,007 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Fuhad Ak (ID: **********): success
2025-07-21 16:54:17,636 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:54:19,061 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 16:54:20,265 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:54:29,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-21 16:54:30,746 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:54:39,798 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:54:43,891 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Fuhad Ak
2025-07-21 16:54:44,469 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e236e7104627ee2037088'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:54:51,903 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:54:57,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:54:57,753 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:54:57,795 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:54:57,795 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:54:57,796 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:54:58,180 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:54:58,211 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:54:59,787 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:55:01,360 - services.user_service - INFO - Created new user: ********** (Sushma_Pediredla)
2025-07-21 16:55:04,213 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sushma_Pediredla (ID: **********): success
2025-07-21 16:55:13,376 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:55:19,121 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:55:27,075 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:55:41,381 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:55:45,918 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Sushma_Pediredla
2025-07-21 16:55:46,451 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_buttons
2025-07-21 16:55:49,434 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:56:00,050 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-21 16:56:00,050 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23ba7104627ee2037089'), 'user_id': **********, 'created_at': 1753097147, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1753097147}
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:56:00,052 - handlers.session_handlers - INFO - Message text: '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 | https://t.me/InstantoPayBot?start=from_channel'
2025-07-21 16:56:00,099 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687d5cc97104627ee2034a6f'), 'status': 'draft', 'admin_id': **********, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERQ5xofVzJ0Ten_PRYr1THitSFh1HeAwACh8UxG_Sv8VdXD05T7e5BsAEAAwIAA3kAAzYE'}, 'updated_at': 1753046285, 'text': '<b>🎉Earn Up to ₹100 Per Referral\nInvite others to join and unlock instant rewards directly in your wallet.\n🎁The more you refer, the more you earn.\n\n☎️ Need help? Contact @instantohelpbot</b>', 'buttons': [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]}
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:56:00,158 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:56:00,159 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:56:00,159 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:56:03,099 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:56:03,888 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:56:03,927 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:56:03,928 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:56:03,928 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:56:03,929 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:56:04,328 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:56:07,198 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:56:20,841 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:22,222 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 16:56:23,625 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:56:24,730 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:56:38,596 - handlers.admin_handlers - ERROR - Error in handle_broadcast_preview: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:56:38,986 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:39,361 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:56:39,361 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:56:41,252 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 16:56:41,253 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23df7104627ee203708a'), 'user_id': **********, 'created_at': 1753097184, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753097184}
2025-07-21 16:56:41,253 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:56:41,254 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:56:41,254 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:56:41,255 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:56:41,255 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:56:42,447 - services.task_service - INFO - Created new task submission: submission_1753097202.4af5fc9c
2025-07-21 16:56:49,298 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:51,002 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:56:51,761 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:56:52,210 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:56:58,120 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 16:56:59,873 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:57:12,431 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:57:13,995 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:57:15,706 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:57:17,622 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:57:17,996 - handlers.callback_handlers - INFO - Processing callback query from user 2069827731: joined
2025-07-21 16:57:20,855 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:57:22,822 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:57:30,457 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 16:57:43,421 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:57:48,505 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:57:51,276 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_start_broadcast
2025-07-21 16:57:52,235 - handlers.admin_handlers - ERROR - Error in handle_confirm_start_broadcast: 'BroadcastService' object has no attribute 'clear_broadcast_draft'
2025-07-21 16:57:58,261 - handlers.callback_handlers - INFO - Processing callback query from user **********: levelRewards
2025-07-21 16:58:04,255 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:58:08,961 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:58:17,130 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:58:21,350 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:58:24,932 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:58:39,005 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_start_broadcast
2025-07-21 16:58:40,451 - handlers.admin_handlers - ERROR - Error in handle_confirm_start_broadcast: 'BroadcastService' object has no attribute 'clear_broadcast_draft'
2025-07-21 16:58:45,381 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:58:53,659 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:58:59,940 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:59:03,805 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:59:04,566 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:59:04,611 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:59:04,611 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:59:04,612 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:59:04,612 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:59:05,013 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:59:06,372 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:59:07,863 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:59:16,946 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:59:18,997 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:59:19,766 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:59:19,811 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:59:19,811 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:59:19,812 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:59:19,812 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:59:20,198 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:59:25,204 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:59:27,038 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:59:35,589 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:59:45,616 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:59:48,314 - handlers.callback_handlers - INFO - Processing callback query from user **********: start_broadcast
2025-07-21 16:59:49,389 - handlers.admin_handlers - ERROR - Error in handle_start_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:59:52,102 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:59:58,211 - services.user_service - INFO - Created new user: ********** (Mohammed)
2025-07-21 17:00:01,131 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mohammed (ID: **********): success
2025-07-21 17:00:03,146 - handlers.callback_handlers - INFO - Processing callback query from user **********: redeemGiftCode
2025-07-21 17:00:15,991 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:00:21,883 - handlers.callback_handlers - INFO - Processing callback query from user **********: levelRewards
2025-07-21 17:00:30,319 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:00:31,987 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:01:24,985 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:01:25,790 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:01:25,832 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:01:26,235 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:01:27,524 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:01:30,395 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_text
2025-07-21 17:01:47,006 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:01:50,559 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:01:53,449 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:03:34,311 - services.user_service - INFO - Created new user: ********** (DAMAN)
2025-07-21 17:03:37,281 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user DAMAN (ID: **********): success
2025-07-21 17:03:52,484 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:05:00,896 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:05:55,711 - services.user_service - INFO - Created new user: ********** (Ayan)
2025-07-21 17:05:58,626 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayan (ID: **********): success
2025-07-21 17:06:06,250 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:06:09,649 - services.user_service - INFO - Created new user: ********** (Anurag)
2025-07-21 17:06:14,026 - handlers.user_handlers - INFO - Notification sent to referrer 5590996532 about new user Anurag (ID: **********): success
2025-07-21 17:06:16,138 - services.user_service - INFO - Created new user: ********** (Lechuz)
2025-07-21 17:06:18,847 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Lechuz (ID: **********): success
2025-07-21 17:06:20,554 - handlers.callback_handlers - INFO - Processing callback query from user 7581293658: taskRewards
2025-07-21 17:06:21,828 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:06:55,737 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:07:02,206 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:07:36,168 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:07:39,703 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:07:43,356 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Lechuz
2025-07-21 17:08:55,149 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:09:21,388 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:43,240 - services.user_service - INFO - Created new user: 7842392725 (Arun)
2025-07-21 17:09:46,236 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: 7842392725): success
2025-07-21 17:09:48,425 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:09:49,908 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:51,851 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:57,361 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: checkSubscription
2025-07-21 17:10:02,367 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: checkSubscription
2025-07-21 17:10:06,309 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 17:10:06,515 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 17:10:12,671 - handlers.callback_handlers - INFO - Processing callback query from user 7842392725: joined
2025-07-21 17:10:16,516 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Arun
2025-07-21 17:10:21,017 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:10:35,216 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:10:37,330 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:13:21,040 - services.user_service - INFO - Created new user: 6056760848 (Arun)
2025-07-21 17:13:25,212 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: 6056760848): success
2025-07-21 17:13:30,777 - handlers.callback_handlers - INFO - Processing callback query from user 6056760848: joined
2025-07-21 17:14:38,808 - handlers.callback_handlers - INFO - Processing callback query from user 6056760848: joined
2025-07-21 17:14:43,228 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Arun
2025-07-21 17:15:33,427 - services.user_service - INFO - Created new user: 7404413593 (𝖇𝖆𝖇𝖚𓃵)
2025-07-21 17:15:36,975 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 𝖇𝖆𝖇𝖚𓃵 (ID: 7404413593): success
2025-07-21 17:16:05,643 - services.user_service - INFO - Created new user: ********** (Arun)
2025-07-21 17:16:09,340 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: **********): success
2025-07-21 17:16:30,995 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:16:35,777 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:16:39,151 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:17:09,135 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:17:14,723 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:17:20,290 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:17:21,849 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:17:48,826 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:18:24,040 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:18:26,234 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:18:27,973 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:18:45,620 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:18:54,406 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:19:00,125 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e292c7104627ee2037090'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:19:23,797 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:19:23,797 - handlers.session_handlers - INFO - Message text: '************'
2025-07-21 17:19:24,233 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: ************
2025-07-21 17:19:24,276 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 17:19:24,277 - services.withdrawal_service - ERROR - Invalid mobile number format: ************ (missing country code)
2025-07-21 17:19:24,277 - handlers.session_handlers - ERROR - Failed to send OTP to ************ for user **********
2025-07-21 17:19:57,554 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e29667104627ee2037091'), 'user_id': **********, 'created_at': 1753098599, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753098599}
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message text: '8606229284'
2025-07-21 17:20:10,691 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: 8606229284
2025-07-21 17:20:10,734 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 17:20:10,735 - services.withdrawal_service - ERROR - Invalid mobile number format: 8606229284 (missing country code)
2025-07-21 17:20:10,735 - handlers.session_handlers - ERROR - Failed to send OTP to 8606229284 for user **********
2025-07-21 17:21:26,135 - handlers.callback_handlers - INFO - Processing callback query from user 7589583468: myWallet
2025-07-21 17:21:50,051 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: joined
2025-07-21 17:21:58,940 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:22:03,818 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: cashOut
2025-07-21 17:22:09,188 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:22:13,594 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:22:21,494 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:22:21,494 - __main__ - INFO - Shutting down bot...
2025-07-21 17:22:22,090 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:22:22,136 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:22:22,137 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:22:28,929 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:22:37,822 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:22:39,494 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:22:39,494 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:22:39,875 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:22:40,832 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:22:40,877 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:22:40,877 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:22:40,877 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:22:40,877 - __main__ - INFO - Starting bot polling...
2025-07-21 17:22:41,085 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:22:41,291 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:22:41,935 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: taskRewards
2025-07-21 17:22:42,316 - handlers.callback_handlers - WARNING - Ignoring expired query 'taskRewards' from user 6677007128
2025-07-21 17:22:42,589 - services.user_service - INFO - Created new user: ********* (Glory)
2025-07-21 17:22:45,390 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Glory (ID: *********): success
2025-07-21 17:22:48,389 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: levelRewards
2025-07-21 17:22:53,507 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:22:56,201 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: redeemGiftCode
2025-07-21 17:23:09,261 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:23:13,138 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:23:16,787 - services.referral_service - INFO - Referral reward processed: 7589583468 earned ₹1 for referring Larry
2025-07-21 17:23:19,844 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:24,365 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:23:28,640 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:34,189 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:23:40,222 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:23:44,846 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:23:50,456 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:54,072 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:23:57,585 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:23:58,824 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:24:00,072 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:24:03,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:24:06,110 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:24:10,316 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:24:13,969 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:17,014 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:24:20,932 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Glory
2025-07-21 17:24:21,476 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:24:26,506 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:29,684 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:24:31,985 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:24:35,695 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:24:38,829 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 17:24:39,037 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 17:24:39,043 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:42,278 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-21 17:24:46,965 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:24:52,712 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:24:55,555 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:58,360 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:24:59,839 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:25:03,355 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:25:06,673 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:25:09,725 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:25:11,360 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:14,398 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:25:20,614 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:25:24,538 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:25:26,508 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:25:31,479 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 17:25:32,759 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:25:33,889 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 17:25:35,102 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:42,175 - services.user_service - INFO - Created new user: 7691292720 (Ayush)
2025-07-21 17:25:44,965 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayush (ID: 7691292720): success
2025-07-21 17:25:46,969 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: joined
2025-07-21 17:25:49,567 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 17:25:49,568 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2ab47104627ee2037094'), 'user_id': **********, 'created_at': 1753098933, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753098933}
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:25:49,570 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:25:49,570 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 17:25:50,499 - services.task_service - INFO - Created new task submission: submission_1753098950.264f91bb
2025-07-21 17:25:51,761 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:25:55,286 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:25:56,512 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:57,969 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: redeemGiftCode
2025-07-21 17:26:01,820 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6677007128, step: redeem_gift_code ===
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2a187104627ee2037092'), 'user_id': 6677007128, 'created_at': 1753098959, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753098959}
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-21 17:26:03,548 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:26:05,691 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:26:06,992 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:26:16,769 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:26:24,049 - handlers.callback_handlers - INFO - Processing callback query from user 7691292720: joined
2025-07-21 17:26:27,046 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:26:30,696 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:26:34,468 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:27:16,829 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:27:20,997 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:27:24,051 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:27:25,453 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:27:27,338 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:27:30,144 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:27:33,016 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:27:37,615 - handlers.callback_handlers - INFO - Processing callback query from user **********: redeemGiftCode
2025-07-21 17:27:41,251 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 17:27:42,464 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:27:46,325 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:27:52,429 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:14,153 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:28:18,196 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:28:28,601 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:42,120 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: joined
2025-07-21 17:28:44,849 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:28:44,850 - __main__ - INFO - Shutting down bot...
2025-07-21 17:28:45,627 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:47,243 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: joined
2025-07-21 17:28:49,378 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:28:49,424 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:28:49,425 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:29:01,414 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:29:11,036 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:29:12,831 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:29:12,831 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:29:13,206 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:29:14,031 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:29:14,066 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:29:14,066 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:29:14,067 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:29:14,067 - __main__ - INFO - Starting bot polling...
2025-07-21 17:29:14,263 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:29:14,469 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:29:15,150 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:16,454 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:17,702 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:29:18,346 - services.user_service - INFO - Created new user: 1027113312 (Bala agency)
2025-07-21 17:29:21,068 - handlers.user_handlers - INFO - Notification sent to referrer 2119928127 about new user Bala agency (ID: 1027113312): success
2025-07-21 17:29:22,542 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:22,915 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user 7894202354
2025-07-21 17:29:24,536 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: customReferralLink
2025-07-21 17:29:24,906 - handlers.callback_handlers - WARNING - Ignoring expired query 'customReferralLink' from user 7894202354
2025-07-21 17:29:34,456 - handlers.callback_handlers - INFO - Processing callback query from user 1027113312: joined
2025-07-21 17:30:09,362 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:30:15,736 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:30:16,490 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:30:16,525 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:30:16,525 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:30:16,526 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:30:16,526 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:30:16,922 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:30:16,951 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:30:21,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 17:30:42,473 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:30:45,766 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:30:46,531 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:30:46,531 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 17:30:46,532 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:30:46,533 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:30:47,733 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2b347104627ee2037097'), 'user_id': **********, 'created_at': 1753099061, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753099061}
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:30:47,735 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:30:47,735 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:30:47,736 - handlers.session_handlers - INFO - Message text: 'Resart'
2025-07-21 17:30:49,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:30:50,114 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:30:50,658 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:30:56,714 - handlers.user_handlers - INFO - Custom referral parameter used: Resart -> User ID: **********
2025-07-21 17:30:59,943 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_buttons
2025-07-21 17:31:00,779 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:31:00,779 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753099246, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 17:31:00,780 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:31:00,780 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:31:02,201 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:31:04,395 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:31:05,148 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:31:05,191 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:31:05,588 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:31:07,928 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:31:11,485 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:32:17,398 - handlers.callback_handlers - INFO - Processing callback query from user 6494779610: myWallet
2025-07-21 17:32:40,105 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:32:43,226 - handlers.callback_handlers - INFO - Processing callback query from user 7880499409: joined
2025-07-21 17:32:49,036 - services.user_service - INFO - Created new user: 7456913321 (Naseem)
2025-07-21 17:32:52,414 - handlers.user_handlers - INFO - Notification sent to referrer 6020716948 about new user Naseem (ID: 7456913321): success
2025-07-21 17:32:54,531 - handlers.callback_handlers - INFO - Processing callback query from user 7880499409: myWallet
2025-07-21 17:33:34,477 - services.user_service - INFO - Created new user: ********** (Hashir)
2025-07-21 17:33:37,570 - handlers.user_handlers - INFO - Notification sent to referrer 7589583468 about new user Hashir (ID: **********): success
2025-07-21 17:33:46,717 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:34:05,177 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:34:45,839 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:35:09,448 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:35:15,139 - services.user_service - INFO - Created new user: ********** (Chan)
2025-07-21 17:35:18,242 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Chan (ID: **********): success
2025-07-21 17:35:19,842 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:35:21,334 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:35:22,756 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 17:35:24,208 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:35:25,669 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:35:28,812 - services.user_service - INFO - Created new user: ********* (Karthik)
2025-07-21 17:35:32,002 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Karthik (ID: *********): success
2025-07-21 17:35:33,705 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:35:35,858 - services.user_service - INFO - Created new user: ********** (Kuldip)
2025-07-21 17:35:38,961 - handlers.user_handlers - INFO - Notification sent to referrer 2119928127 about new user Kuldip (ID: **********): success
2025-07-21 17:35:40,566 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:35:43,621 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:35:49,307 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:35:58,518 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:36:06,404 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:36:08,051 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:10,002 - handlers.callback_handlers - INFO - Processing callback query from user *********: checkSubscription
2025-07-21 17:36:21,668 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:36:25,858 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Karthik
2025-07-21 17:36:26,395 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:31,884 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: customReferralLink
2025-07-21 17:36:34,511 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:36:37,384 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:39,467 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:36:41,479 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:43,624 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7940369121, step: create_custom_referral ===
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2d477104627ee203709b'), 'user_id': 7940369121, 'created_at': 1753099592, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753099592}
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message text: 'https://t.me/InstantoPayBot?start=7940369121
I've Got Up To ₹100! Click URL To Join'
2025-07-21 17:36:47,466 - handlers.callback_handlers - INFO - Processing callback query from user *********: myWallet
2025-07-21 17:36:52,536 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:36:54,060 - handlers.callback_handlers - INFO - Processing callback query from user *********: extraRewards
2025-07-21 17:36:56,640 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:36:59,583 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:04,684 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:37:06,312 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:37:07,974 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:37:21,163 - handlers.callback_handlers - INFO - Processing callback query from user 6910817594: extraRewards
2025-07-21 17:37:24,504 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:37:38,919 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:37:43,702 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:45,810 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:47,751 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:37:50,584 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:37:52,570 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:37:54,485 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:37:55,284 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:37:59,649 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 17:38:03,512 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawal_method_bank
2025-07-21 17:38:09,407 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:38:22,454 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:38:31,735 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:38:38,322 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:39:06,735 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:39:10,620 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:39:15,388 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:39:20,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:39:25,999 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:39:32,789 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:39:52,424 - handlers.callback_handlers - INFO - Processing callback query from user 7930384713: withdrawalRecord
2025-07-21 17:39:56,011 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 17:39:57,576 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2e157104627ee203709d'), 'user_id': **********, 'created_at': 1753099798, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753099798}
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 17:40:17,343 - services.task_service - INFO - Created new task submission: submission_1753099817.c3f1fbc2
2025-07-21 17:40:21,429 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:40:27,564 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:41:44,623 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: joined
2025-07-21 17:42:03,058 - services.user_service - INFO - Created new user: 7346740073 (Mujahid)
2025-07-21 17:42:05,902 - handlers.user_handlers - INFO - Notification sent to referrer 7589583468 about new user Mujahid (ID: 7346740073): success
2025-07-21 17:42:08,716 - services.user_service - INFO - Created new user: 1913711815 (Alone boy)
2025-07-21 17:42:11,467 - handlers.user_handlers - INFO - Notification sent to referrer 7683380744 about new user Alone boy (ID: 1913711815): success
2025-07-21 17:42:14,402 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: checkSubscription
2025-07-21 17:42:19,270 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:42:19,270 - __main__ - INFO - Shutting down bot...
2025-07-21 17:42:19,881 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:42:19,928 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:42:19,929 - __main__ - INFO - Bot shutdown completed
