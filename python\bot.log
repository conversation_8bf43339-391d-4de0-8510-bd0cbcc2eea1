2025-07-21 16:26:16,262 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:26:18,343 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:18,344 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:21,106 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,173 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,174 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:27,077 - services.user_service - INFO - Created new user: ********** (Getha)
2025-07-21 16:26:28,478 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:26:30,267 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Getha (ID: **********): success
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:48,802 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:59,397 - services.user_service - INFO - Created new user: 8135061794 (Someshhh)
2025-07-21 16:27:00,564 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:01,883 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Someshhh (ID: 8135061794): success
2025-07-21 16:27:12,254 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Getha
2025-07-21 16:27:39,740 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Someshhh
2025-07-21 16:27:40,568 - services.user_service - INFO - Created new user: ********** (Goat)
2025-07-21 16:27:41,848 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:43,426 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Goat (ID: **********): success
2025-07-21 16:28:42,357 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:28:45,850 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1d517104627ee2037073'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:28:48,852 - services.promotion_report_service - INFO - User ********** has 274 referrals, using optimized calculation
2025-07-21 16:28:54,619 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:01,835 - handlers.callback_handlers - ERROR - Error in _handle_redeem_gift_code: Message to edit not found
2025-07-21 16:29:03,431 - services.user_service - INFO - Created new user: 1963388963 (Sudip)
2025-07-21 16:29:04,582 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:29:05,865 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sudip (ID: 1963388963): success
2025-07-21 16:29:19,356 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095559.196fdefa
2025-07-21 16:29:25,699 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:29,919 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Sudip
2025-07-21 16:29:55,103 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:29:55,104 - __main__ - INFO - Shutting down bot...
2025-07-21 16:29:55,791 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:29:55,835 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:29:55,836 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:30:02,106 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:30:15,085 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:30:16,750 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:30:16,751 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:30:17,163 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:30:18,103 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:30:18,155 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:30:18,155 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:30:18,156 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:30:18,156 - __main__ - INFO - Starting bot polling...
2025-07-21 16:30:18,366 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:30:18,579 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,023 - handlers.callback_handlers - ERROR - Error in callback handler for data 'view_user_full_details_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:20,776 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:30:25,189 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:28,867 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:30,629 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:32,453 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:30:43,246 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-21 16:30:43,248 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1dd87104627ee2037076'), 'user_id': **********, 'created_at': 1753095640, 'data': {}, 'step': 'user_details_get_id', 'updated_at': 1753095640}
2025-07-21 16:30:43,249 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:30:43,250 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:30:43,251 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:30:43,252 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 16:31:29,101 - services.user_service - INFO - Created new user: 7073225065 (Kenneth)
2025-07-21 16:31:30,296 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:31,663 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Kenneth (ID: 7073225065): success
2025-07-21 16:31:33,466 - services.user_service - INFO - Created new user: 5274518837 (Sangram)
2025-07-21 16:31:34,814 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:31:36,147 - handlers.user_handlers - INFO - Notification sent to referrer 7894803745 about new user Sangram (ID: 5274518837): success
2025-07-21 16:31:51,265 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 16:31:52,066 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-21 16:31:52,477 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 16:31:52,524 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-21 16:31:52,625 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 16:31:52,683 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 16:31:53,688 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:54,545 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 16:31:58,821 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Kenneth
2025-07-21 16:32:05,472 - services.referral_service - INFO - Referral reward processed: 7894803745 earned ₹2 for referring Sangram
2025-07-21 16:32:08,633 - services.user_service - INFO - Created new user: 2069827731 (Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS)
2025-07-21 16:32:10,122 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:11,470 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Gabriel 🐦 SUI 💛 Sons of Ton 🐾 CATTOS (ID: 2069827731): success
2025-07-21 16:32:21,050 - services.user_service - INFO - Created new user: ********** (Randeep)
2025-07-21 16:32:22,390 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:32:23,698 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Randeep (ID: **********): success
2025-07-21 16:32:26,652 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:32:32,708 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:33:22,106 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095802.fe75e18b
2025-07-21 16:33:30,631 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:33:40,194 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:34:10,330 - services.user_service - INFO - Created new user: 6307558097 (Vishal)
2025-07-21 16:34:11,555 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:34:12,965 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user Vishal (ID: 6307558097): success
2025-07-21 16:34:25,573 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 16:34:25,788 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 16:34:42,936 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:34:56,679 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹1 for referring Randeep
2025-07-21 16:35:04,019 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹3 for referring Vishal
2025-07-21 16:35:22,460 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Goat
2025-07-21 16:35:36,480 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:35:36,480 - __main__ - INFO - Shutting down bot...
2025-07-21 16:35:37,108 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:35:37,154 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:35:37,155 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:35:54,105 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:36:07,056 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:36:08,811 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:36:08,812 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:36:11,915 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:36:12,986 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:36:13,032 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:36:13,042 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:36:13,043 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:36:13,056 - __main__ - INFO - Starting bot polling...
2025-07-21 16:36:13,274 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:36:13,487 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:36:14,456 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:36:16,954 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:17,326 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:17,720 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:18,549 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:18,550 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:18,919 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,093 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:19,467 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,841 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,842 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:19,843 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:19,844 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:20,591 - handlers.user_handlers - ERROR - Failed to edit error message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:20,592 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:20,961 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:36:20,962 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:21,322 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:21,324 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:21,792 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,171 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,172 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:22,173 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:22,543 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:22,545 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:22,920 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,290 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,291 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:23,292 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:23,292 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,067 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:24,838 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:25,584 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:27,571 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:29,761 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:31,213 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:32,599 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 16:36:33,968 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 16:36:35,492 - services.promotion_report_service - INFO - User ********** has 51 referrals, using optimized calculation
2025-07-21 16:36:35,890 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:37,860 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:39,132 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:36:40,401 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:36:43,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:36:43,757 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_approval_confirmation: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:43,758 - handlers.admin_handlers - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 6834, in handle_withdrawal_approval_confirmation
    await query.answer("Loading withdrawal details...")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 187, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 900, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3767, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid

2025-07-21 16:36:44,132 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approve_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:44,581 - handlers.callback_handlers - WARNING - Query expired for data 'approve_withdrawal_**********' from user **********
2025-07-21 16:36:44,581 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: joined
2025-07-21 16:36:44,951 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:36:45,574 - services.user_service - INFO - Created new user: ********** (Rintu)
2025-07-21 16:36:48,616 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Rintu (ID: **********): success
2025-07-21 16:36:50,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:51,955 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:36:53,486 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:36:55,012 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:36:55,390 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:36:59,280 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 16:37:00,827 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:02,955 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:03,326 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:05,284 - handlers.callback_handlers - INFO - Processing callback query from user 5738047104: extraRewards
2025-07-21 16:37:09,477 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_approve_withdrawal_**********
2025-07-21 16:37:12,689 - services.withdrawal_service - INFO - Withdrawal approved: User **********, Amount ₹100
2025-07-21 16:37:13,499 - handlers.admin_handlers - INFO - Admin ********** (Kêviñ) approved withdrawal for user ********** (₹100)
2025-07-21 16:37:13,816 - services.user_service - INFO - Created new user: ********** (S)
2025-07-21 16:37:16,661 - handlers.user_handlers - INFO - Notification sent to referrer 7264917899 about new user S (ID: **********): success
2025-07-21 16:37:18,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:22,452 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:37:35,323 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:37,124 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:38,450 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:37:38,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:40,726 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:37:44,246 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:53,028 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:37:57,636 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:37:59,033 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:38:02,624 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:38:05,230 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:38:06,426 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:38:09,021 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1f957104627ee203707b'), 'user_id': **********, 'created_at': 1753096085, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753096085}
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:38:09,023 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:38:09,024 - handlers.session_handlers - INFO - Message text: 'https://t.me/InstantoPayBot?start=**********
I've Got Up To ₹100! Click URL To Join'
2025-07-21 16:38:37,729 - services.user_service - INFO - Created new user: ********** (4meen)
2025-07-21 16:38:40,437 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 4meen (ID: **********): success
2025-07-21 16:38:57,463 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:39:01,313 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:10,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:39:12,140 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:17,003 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:39:18,373 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:39:47,661 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:52,569 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:39:59,812 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:40:08,825 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 16:40:10,402 - services.user_service - INFO - Created new user: ********** (Vishnu Vardhan)
2025-07-21 16:40:13,361 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Vishnu Vardhan (ID: **********): success
2025-07-21 16:40:46,436 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:14,651 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:17,209 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:41:20,904 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring 4meen
2025-07-21 16:41:21,461 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:41:29,981 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:41:40,574 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:41:50,552 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:42:02,986 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:04,703 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:42:12,345 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:42:14,714 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 16:42:20,922 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:42:26,961 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:42:36,510 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:42:47,097 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:42:49,869 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:43:27,714 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:43:40,646 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:43:42,273 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:43:42,273 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:43:42,713 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:43:43,642 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:43:43,686 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:43:43,686 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:43:43,686 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:43:43,686 - __main__ - INFO - Starting bot polling...
2025-07-21 16:43:43,902 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:43:44,118 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:43:51,568 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:44:01,252 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:44:09,431 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:44:12,931 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:44:21,404 - services.user_service - INFO - Created new user: 1253475096 (Prakash)
2025-07-21 16:44:24,458 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Prakash (ID: 1253475096): success
2025-07-21 16:44:26,925 - services.user_service - INFO - Created new user: ********** (Ranjitha)
2025-07-21 16:44:29,832 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ranjitha (ID: **********): success
2025-07-21 16:45:13,462 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:45:14,746 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:45:14,792 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:45:14,793 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:45:14,793 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:45:15,012 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:15,227 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:15,266 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:45:15,660 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:45:19,884 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Ranjitha
2025-07-21 16:45:22,610 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:45:25,454 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:45:26,217 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:45:26,265 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:45:26,265 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:45:26,266 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:45:26,479 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:26,689 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:45:26,694 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:45:32,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:45:38,548 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:45:44,806 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:45:55,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 16:45:57,512 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:46:00,707 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:46:02,472 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:46:03,256 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:46:03,300 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: False
2025-07-21 16:46:03,300 - handlers.admin_handlers - INFO - Creating keyboard with 3 rows
2025-07-21 16:46:03,301 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:46:03,681 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:46:07,575 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:46:09,335 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:46:13,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:46:15,678 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:46:17,802 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:46:21,351 - services.referral_service - INFO - Referral reward processed: 7264917899 earned ₹2 for referring S
2025-07-21 16:46:33,965 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:46:46,876 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e216d7104627ee203707d'), 'user_id': **********, 'created_at': 1753096558, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753096558}
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:46:52,544 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:46:53,515 - services.task_service - INFO - Created new task submission: submission_1753096613.ac184a7d
2025-07-21 16:47:03,258 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:47:05,014 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:47:10,112 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:47:12,200 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:47:12,971 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:47:13,012 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:47:13,013 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:47:13,013 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:47:13,220 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:13,429 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:13,431 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:47:17,587 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:47:21,459 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:47:22,223 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:47:22,267 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:47:22,267 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:47:22,268 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:47:22,479 - handlers.admin_handlers - ERROR - Error creating/sending keyboard: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:22,686 - handlers.admin_handlers - ERROR - Error in handle_user_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:47:22,689 - handlers.admin_handlers - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1027, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\referbot\python\handlers\admin_handlers.py", line 1036, in handle_user_broadcast
    await query.edit_message_text(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_callbackquery.py", line 264, in edit_message_text
    return await self._get_message().edit_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_message.py", line 3436, in edit_text
    return await self.get_bot().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 1618, in edit_message_text
    return await super().edit_message_text(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 3868, in edit_message_text
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 581, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 736, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end tag corresponding to start tag "b"

2025-07-21 16:47:24,790 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:47:26,062 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:47:35,052 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 16:47:39,995 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:47:58,494 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:48:56,478 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:49:02,283 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e21d07104627ee203707e'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:49:02,284 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:49:02,285 - handlers.session_handlers - INFO - Message text: 'AC/NO -***********
IFSC CODE-MAHB0000437
NAME- PRAFUL DEVRAO SORTE'
2025-07-21 16:49:11,777 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 16:49:35,619 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e21d07104627ee203707e'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:49:35,620 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:49:35,621 - handlers.session_handlers - INFO - Message text: '***********'
2025-07-21 16:49:36,072 - services.withdrawal_service - INFO - Duplicate check for account ***********: found 0 matches
2025-07-21 16:49:36,871 - services.withdrawal_service - INFO - Duplicate check for account ***********: found 0 matches
2025-07-21 16:49:42,934 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:49:59,732 - handlers.callback_handlers - INFO - Processing callback query from user **********: set name
2025-07-21 16:50:18,646 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-21 16:50:18,648 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22607104627ee203707f'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-21 16:50:18,648 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:50:18,649 - handlers.session_handlers - INFO - Message text: 'Praful sorte'
2025-07-21 16:50:26,315 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:50:32,114 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 16:50:41,644 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 16:50:41,644 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22807104627ee2037080'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:50:41,645 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:50:41,646 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:50:41,646 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 16:50:42,146 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: **********
2025-07-21 16:50:42,190 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 16:50:42,192 - services.withdrawal_service - ERROR - Invalid mobile number format: ********** (missing country code)
2025-07-21 16:50:42,192 - handlers.session_handlers - ERROR - Failed to send OTP to ********** for user **********
2025-07-21 16:50:43,385 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:50:53,010 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:51:02,979 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 16:51:23,976 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 16:51:23,976 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22a07104627ee2037081'), 'user_id': **********, 'created_at': 1753096864, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753096864}
2025-07-21 16:51:23,977 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:51:23,978 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:51:23,978 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:51:23,979 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:51:23,979 - handlers.session_handlers - INFO - Message text: '+91**********'
2025-07-21 16:51:25,195 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +91**********
2025-07-21 16:51:25,244 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 16:51:25,244 - services.withdrawal_service - INFO - Converted +91********** to API format: **********
2025-07-21 16:51:25,245 - services.withdrawal_service - INFO - Sending OTP to +91********** (API format: **********) using URL: https://sms.renflair.in/V1.php
2025-07-21 16:51:27,829 - services.withdrawal_service - INFO - OTP API response for +91**********: Status 200, Content: {"return":true,"request_id":"f9Nlw0BujsMZzgh","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - OTP API analysis for +91********** (API format: **********): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - OTP API call successful for +91**********. OTP: 2039
2025-07-21 16:51:27,831 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-21 16:51:28,833 - handlers.session_handlers - INFO - OTP API call successful for +91**********, user ********** moved to verification step
2025-07-21 16:51:37,203 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:51:37,203 - __main__ - INFO - Shutting down bot...
2025-07-21 16:51:37,849 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:51:37,892 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:51:37,892 - __main__ - INFO - Bot shutdown completed
2025-07-21 16:51:43,566 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 16:51:56,559 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 16:51:58,171 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 16:51:58,172 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 16:51:58,686 - __main__ - INFO - All handlers registered successfully
2025-07-21 16:51:59,577 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 16:51:59,618 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 16:51:59,618 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 16:51:59,618 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 16:51:59,619 - __main__ - INFO - Starting bot polling...
2025-07-21 16:51:59,825 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 16:52:00,032 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 16:52:00,747 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:52:02,690 - services.user_service - INFO - Created new user: ********** (Sham)
2025-07-21 16:52:05,506 - handlers.user_handlers - INFO - Notification sent to referrer 7802823735 about new user Sham (ID: **********): success
2025-07-21 16:52:07,131 - handlers.callback_handlers - INFO - Processing callback query from user **********: gift_broadcast
2025-07-21 16:52:07,516 - handlers.callback_handlers - WARNING - Ignoring expired query 'gift_broadcast' from user **********
2025-07-21 16:52:07,776 - services.user_service - INFO - Created new user: ********** (Ayush)
2025-07-21 16:52:10,741 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayush (ID: **********): success
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22a07104627ee2037081'), 'user_id': **********, 'created_at': 1753096887, 'data': {'mobile_number': '+91**********', 'otp': '2039', 'otp_timestamp': 1753096887}, 'step': 'verify_otp', 'updated_at': 1753096887}
2025-07-21 16:52:12,371 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:12,372 - handlers.session_handlers - INFO - Message text: '2039'
2025-07-21 16:52:13,975 - handlers.callback_handlers - INFO - Processing callback query from user **********: gift_broadcast
2025-07-21 16:52:17,206 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:52:20,121 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:52:33,697 - handlers.callback_handlers - INFO - Processing callback query from user **********: set ifsc
2025-07-21 16:52:35,136 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:52:35,902 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:52:35,946 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: False
2025-07-21 16:52:35,947 - handlers.admin_handlers - INFO - Creating keyboard with 3 rows
2025-07-21 16:52:35,947 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:52:36,350 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:52:38,043 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_text
2025-07-21 16:52:40,413 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_text ===
2025-07-21 16:52:40,414 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22fe7104627ee2037084'), 'user_id': **********, 'created_at': 1753096959, 'data': {}, 'step': 'broadcast_text', 'updated_at': 1753096959}
2025-07-21 16:52:40,415 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:40,415 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:40,416 - handlers.session_handlers - INFO - Message text: 'jkjhk'
2025-07-21 16:52:40,461 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Data to save: {'text': 'jkjhk'}
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:52:40,462 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:52:40,512 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:52:40,512 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Matched count: 0
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Modified count: 0
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Upserted ID: 687e22ff7104627ee2037085
2025-07-21 16:52:40,513 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:52:41,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:52:45,579 - services.referral_service - INFO - Referral reward processed: 7802823735 earned ₹5 for referring Sham
2025-07-21 16:52:46,128 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:52:46,883 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:52:46,928 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:52:46,929 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:52:46,929 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:52:47,325 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:52:50,422 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_buttons
2025-07-21 16:52:51,664 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e22fa7104627ee2037083'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:51,665 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:51,666 - handlers.session_handlers - INFO - Message text: 'MAHB0000437'
2025-07-21 16:52:53,159 - utils.helpers - INFO - Cached bank details for IFSC: MAHB0000437
2025-07-21 16:52:59,658 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-21 16:52:59,659 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e230a7104627ee2037086'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': **********}
2025-07-21 16:52:59,659 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:52:59,660 - handlers.session_handlers - INFO - Message text: 'Visit Website | https://example.com
Join Channel | https://t.me/channel'
2025-07-21 16:52:59,700 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:52:59,701 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096960, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]}
2025-07-21 16:52:59,701 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:52:59,702 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:52:59,749 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:52:59,751 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:53:01,696 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:53:02,490 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:53:02,532 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:02,951 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:02,952 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:53:06,751 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:53:19,274 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:53:23,120 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Ayush
2025-07-21 16:53:23,672 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_MAHB0000437
2025-07-21 16:53:25,606 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:53:27,544 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_MAHB0000437
2025-07-21 16:53:28,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:53:29,110 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:29,151 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:53:29,152 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:29,542 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:31,884 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_media
2025-07-21 16:53:44,433 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:53:46,491 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_media ===
2025-07-21 16:53:46,491 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23347104627ee2037087'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_media', 'updated_at': **********}
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:53:46,493 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - === ROUTING TO BROADCAST MEDIA HANDLER for user ********** ===
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING START for user ********** ===
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message object exists: True
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message ID: 1136373
2025-07-21 16:53:46,494 - handlers.session_handlers - INFO - Message text: None
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message caption: None
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - === CHECKING MEDIA TYPES for user ********** ===
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has audio: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has voice: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has sticker: False
2025-07-21 16:53:46,495 - handlers.session_handlers - INFO - Message has animation: False
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - Message has video_note: False
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - ✅ PHOTO detected - file_id: AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - === MEDIA PROCESSING SUCCESS for user ********** ===
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - Media data created: {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}
2025-07-21 16:53:46,496 - handlers.session_handlers - INFO - === SAVING TO BROADCAST DRAFT for user ********** ===
2025-07-21 16:53:46,497 - handlers.session_handlers - INFO - Getting existing broadcast draft for user **********
2025-07-21 16:53:46,541 - handlers.session_handlers - INFO - Existing draft: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]}
2025-07-21 16:53:46,542 - handlers.session_handlers - INFO - Updated draft with media: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]], 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 16:53:46,542 - handlers.session_handlers - INFO - Calling save_broadcast_draft for user **********
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753096979, 'buttons': [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]], 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:53:46,544 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:53:46,592 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:53:46,593 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:53:46,594 - handlers.session_handlers - INFO - === BROADCAST DRAFT SAVE RESULT for user **********: True ===
2025-07-21 16:53:46,594 - handlers.session_handlers - INFO - Clearing session for user **********
2025-07-21 16:53:46,641 - handlers.session_handlers - INFO - Session cleared for user **********
2025-07-21 16:53:46,642 - handlers.session_handlers - INFO - === SENDING SUCCESS MESSAGE to user ********** ===
2025-07-21 16:53:47,050 - handlers.session_handlers - INFO - ✅ SUCCESS MESSAGE SENT to user **********
2025-07-21 16:53:47,051 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING END for user ********** ===
2025-07-21 16:53:48,770 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:53:49,522 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:53:49,567 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:53:49,567 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 16:53:49,568 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:53:49,569 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:53:49,972 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:53:51,599 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:53:56,239 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:54:11,211 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:54:13,180 - services.user_service - INFO - Created new user: ********** (Fuhad Ak)
2025-07-21 16:54:16,007 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Fuhad Ak (ID: **********): success
2025-07-21 16:54:17,636 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:54:19,061 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 16:54:20,265 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:54:29,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-21 16:54:30,746 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:54:31,499 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:54:31,551 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:54:39,798 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:54:43,891 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Fuhad Ak
2025-07-21 16:54:44,469 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e236e7104627ee2037088'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:54:44,470 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:54:44,471 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:54:51,903 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 16:54:57,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:54:57,753 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:54:57,795 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:54:57,795 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 16:54:57,796 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:54:58,180 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:54:58,211 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:54:59,787 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:55:01,360 - services.user_service - INFO - Created new user: ********** (Sushma_Pediredla)
2025-07-21 16:55:04,213 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sushma_Pediredla (ID: **********): success
2025-07-21 16:55:13,376 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:55:19,121 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:55:27,075 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:55:41,381 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:55:45,918 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Sushma_Pediredla
2025-07-21 16:55:46,451 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_buttons
2025-07-21 16:55:49,434 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:56:00,050 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-21 16:56:00,050 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23ba7104627ee2037089'), 'user_id': **********, 'created_at': 1753097147, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1753097147}
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:56:00,051 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:56:00,052 - handlers.session_handlers - INFO - Message text: '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 | https://t.me/InstantoPayBot?start=from_channel'
2025-07-21 16:56:00,099 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687d5cc97104627ee2034a6f'), 'status': 'draft', 'admin_id': **********, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERQ5xofVzJ0Ten_PRYr1THitSFh1HeAwACh8UxG_Sv8VdXD05T7e5BsAEAAwIAA3kAAzYE'}, 'updated_at': 1753046285, 'text': '<b>🎉Earn Up to ₹100 Per Referral\nInvite others to join and unlock instant rewards directly in your wallet.\n🎁The more you refer, the more you earn.\n\n☎️ Need help? Contact @instantohelpbot</b>', 'buttons': [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]}
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 16:56:00,102 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 16:56:00,158 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 16:56:00,159 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 16:56:00,159 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 16:56:00,160 - services.broadcast_service - INFO - Returning success: True
2025-07-21 16:56:03,099 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:56:03,888 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:56:03,927 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:56:03,928 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:56:03,928 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:56:03,929 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:56:04,328 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:56:07,198 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:56:20,841 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:22,222 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 16:56:23,625 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:56:24,730 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:56:38,596 - handlers.admin_handlers - ERROR - Error in handle_broadcast_preview: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:56:38,986 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:39,361 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-21 16:56:39,361 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:56:41,252 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 16:56:41,253 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e23df7104627ee203708a'), 'user_id': **********, 'created_at': 1753097184, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753097184}
2025-07-21 16:56:41,253 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 16:56:41,254 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 16:56:41,254 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:56:41,255 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:56:41,255 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 16:56:42,447 - services.task_service - INFO - Created new task submission: submission_1753097202.4af5fc9c
2025-07-21 16:56:49,298 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:56:51,002 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:56:51,761 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:56:51,805 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:56:52,210 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:56:58,120 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 16:56:59,873 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 16:57:12,431 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:57:13,995 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:57:15,706 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:57:17,622 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:57:17,996 - handlers.callback_handlers - INFO - Processing callback query from user 2069827731: joined
2025-07-21 16:57:20,855 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:57:22,822 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:57:30,457 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 16:57:43,421 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 16:57:48,505 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:57:51,276 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_start_broadcast
2025-07-21 16:57:52,235 - handlers.admin_handlers - ERROR - Error in handle_confirm_start_broadcast: 'BroadcastService' object has no attribute 'clear_broadcast_draft'
2025-07-21 16:57:58,261 - handlers.callback_handlers - INFO - Processing callback query from user **********: levelRewards
2025-07-21 16:58:04,255 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:58:08,961 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:58:17,130 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:58:21,350 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:58:24,932 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:58:39,005 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_start_broadcast
2025-07-21 16:58:40,451 - handlers.admin_handlers - ERROR - Error in handle_confirm_start_broadcast: 'BroadcastService' object has no attribute 'clear_broadcast_draft'
2025-07-21 16:58:45,381 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 16:58:53,659 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 16:58:59,940 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:59:03,805 - handlers.callback_handlers - INFO - Processing callback query from user **********: userBroadcast
2025-07-21 16:59:04,566 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:59:04,611 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:59:04,611 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:59:04,612 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:59:04,612 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:59:05,013 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:59:06,372 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:59:07,863 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:59:16,946 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 16:59:18,997 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 16:59:19,766 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 16:59:19,811 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 16:59:19,811 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 16:59:19,812 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 16:59:19,812 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 16:59:20,198 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 16:59:25,204 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 16:59:27,038 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:59:35,589 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 16:59:45,616 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 16:59:48,314 - handlers.callback_handlers - INFO - Processing callback query from user **********: start_broadcast
2025-07-21 16:59:49,389 - handlers.admin_handlers - ERROR - Error in handle_start_broadcast: Can't parse entities: can't find end tag corresponding to start tag "b"
2025-07-21 16:59:52,102 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 16:59:58,211 - services.user_service - INFO - Created new user: ********** (Mohammed)
2025-07-21 17:00:01,131 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mohammed (ID: **********): success
2025-07-21 17:00:03,146 - handlers.callback_handlers - INFO - Processing callback query from user **********: redeemGiftCode
2025-07-21 17:00:15,991 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:00:21,883 - handlers.callback_handlers - INFO - Processing callback query from user **********: levelRewards
2025-07-21 17:00:30,319 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:00:31,987 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:01:24,985 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:01:25,790 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:01:25,832 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:01:25,833 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:01:26,235 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:01:27,524 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:01:30,395 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_text
2025-07-21 17:01:47,006 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:01:50,559 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:01:53,449 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:03:34,311 - services.user_service - INFO - Created new user: ********** (DAMAN)
2025-07-21 17:03:37,281 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user DAMAN (ID: **********): success
2025-07-21 17:03:52,484 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:05:00,896 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:05:55,711 - services.user_service - INFO - Created new user: ********** (Ayan)
2025-07-21 17:05:58,626 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayan (ID: **********): success
2025-07-21 17:06:06,250 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:06:09,649 - services.user_service - INFO - Created new user: ********** (Anurag)
2025-07-21 17:06:14,026 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Anurag (ID: **********): success
2025-07-21 17:06:16,138 - services.user_service - INFO - Created new user: ********** (Lechuz)
2025-07-21 17:06:18,847 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Lechuz (ID: **********): success
2025-07-21 17:06:20,554 - handlers.callback_handlers - INFO - Processing callback query from user 7581293658: taskRewards
2025-07-21 17:06:21,828 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:06:55,737 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:07:02,206 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:07:36,168 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:07:39,703 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:07:43,356 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Lechuz
2025-07-21 17:08:55,149 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:09:21,388 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:43,240 - services.user_service - INFO - Created new user: 7842392725 (Arun)
2025-07-21 17:09:46,236 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: 7842392725): success
2025-07-21 17:09:48,425 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:09:49,908 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:51,851 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:09:57,361 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: checkSubscription
2025-07-21 17:10:02,367 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: checkSubscription
2025-07-21 17:10:06,309 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 17:10:06,515 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 17:10:12,671 - handlers.callback_handlers - INFO - Processing callback query from user 7842392725: joined
2025-07-21 17:10:16,516 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Arun
2025-07-21 17:10:21,017 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:10:35,216 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:10:37,330 - handlers.callback_handlers - INFO - Processing callback query from user 7932302469: joined
2025-07-21 17:13:21,040 - services.user_service - INFO - Created new user: 6056760848 (Arun)
2025-07-21 17:13:25,212 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: 6056760848): success
2025-07-21 17:13:30,777 - handlers.callback_handlers - INFO - Processing callback query from user 6056760848: joined
2025-07-21 17:14:38,808 - handlers.callback_handlers - INFO - Processing callback query from user 6056760848: joined
2025-07-21 17:14:43,228 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Arun
2025-07-21 17:15:33,427 - services.user_service - INFO - Created new user: 7404413593 (𝖇𝖆𝖇𝖚𓃵)
2025-07-21 17:15:36,975 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 𝖇𝖆𝖇𝖚𓃵 (ID: 7404413593): success
2025-07-21 17:16:05,643 - services.user_service - INFO - Created new user: ********** (Arun)
2025-07-21 17:16:09,340 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arun (ID: **********): success
2025-07-21 17:16:30,995 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:16:35,777 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:16:39,151 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:17:09,135 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:17:14,723 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:17:20,290 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:17:21,849 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:17:48,826 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:18:24,040 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:18:26,234 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:18:27,973 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:18:45,620 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:18:54,406 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:19:00,125 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e292c7104627ee2037090'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:19:23,796 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:19:23,797 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:19:23,797 - handlers.session_handlers - INFO - Message text: '************'
2025-07-21 17:19:24,233 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: ************
2025-07-21 17:19:24,276 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 17:19:24,277 - services.withdrawal_service - ERROR - Invalid mobile number format: ************ (missing country code)
2025-07-21 17:19:24,277 - handlers.session_handlers - ERROR - Failed to send OTP to ************ for user **********
2025-07-21 17:19:57,554 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e29667104627ee2037091'), 'user_id': **********, 'created_at': 1753098599, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753098599}
2025-07-21 17:20:10,244 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:20:10,246 - handlers.session_handlers - INFO - Message text: '8606229284'
2025-07-21 17:20:10,691 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: 8606229284
2025-07-21 17:20:10,734 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 17:20:10,735 - services.withdrawal_service - ERROR - Invalid mobile number format: 8606229284 (missing country code)
2025-07-21 17:20:10,735 - handlers.session_handlers - ERROR - Failed to send OTP to 8606229284 for user **********
2025-07-21 17:21:26,135 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:21:50,051 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: joined
2025-07-21 17:21:58,940 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:22:03,818 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: cashOut
2025-07-21 17:22:09,188 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:22:13,594 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:22:21,494 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:22:21,494 - __main__ - INFO - Shutting down bot...
2025-07-21 17:22:22,090 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:22:22,136 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:22:22,137 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:22:28,929 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:22:37,822 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:22:39,494 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:22:39,494 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:22:39,875 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:22:40,832 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:22:40,877 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:22:40,877 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:22:40,877 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:22:40,877 - __main__ - INFO - Starting bot polling...
2025-07-21 17:22:41,085 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:22:41,291 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:22:41,935 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: taskRewards
2025-07-21 17:22:42,316 - handlers.callback_handlers - WARNING - Ignoring expired query 'taskRewards' from user 6677007128
2025-07-21 17:22:42,589 - services.user_service - INFO - Created new user: ********* (Glory)
2025-07-21 17:22:45,390 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Glory (ID: *********): success
2025-07-21 17:22:48,389 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: levelRewards
2025-07-21 17:22:53,507 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:22:56,201 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: redeemGiftCode
2025-07-21 17:23:09,261 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:23:13,138 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:23:16,787 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Larry
2025-07-21 17:23:19,844 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:24,365 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:23:28,640 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:34,189 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:23:40,222 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:23:44,846 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:23:50,456 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:23:54,072 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:23:57,585 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:23:58,824 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:24:00,072 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:24:03,019 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:24:06,110 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:24:10,316 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:24:13,969 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:17,014 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:24:20,932 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Glory
2025-07-21 17:24:21,476 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:24:26,506 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:29,684 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:24:31,985 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:24:35,695 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:24:38,829 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-21 17:24:39,037 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-21 17:24:39,043 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:42,278 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-21 17:24:46,965 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:24:52,712 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:24:55,555 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:24:58,360 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:24:59,839 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:25:03,355 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:25:06,673 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:25:09,725 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:25:11,360 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:14,398 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:25:20,614 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:25:24,538 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:25:26,508 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:25:31,479 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 17:25:32,759 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:25:33,889 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 17:25:35,102 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:42,175 - services.user_service - INFO - Created new user: 7691292720 (Ayush)
2025-07-21 17:25:44,965 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ayush (ID: 7691292720): success
2025-07-21 17:25:46,969 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: joined
2025-07-21 17:25:49,567 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 17:25:49,568 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2ab47104627ee2037094'), 'user_id': **********, 'created_at': 1753098933, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753098933}
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:25:49,569 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:25:49,570 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:25:49,570 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 17:25:50,499 - services.task_service - INFO - Created new task submission: submission_1753098950.264f91bb
2025-07-21 17:25:51,761 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: myWallet
2025-07-21 17:25:55,286 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: extraRewards
2025-07-21 17:25:56,512 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:25:57,969 - handlers.callback_handlers - INFO - Processing callback query from user 6677007128: redeemGiftCode
2025-07-21 17:26:01,820 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6677007128, step: redeem_gift_code ===
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2a187104627ee2037092'), 'user_id': 6677007128, 'created_at': 1753098959, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753098959}
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:26:01,821 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:26:01,822 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-21 17:26:03,548 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:26:05,691 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:26:06,992 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:26:16,769 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:26:24,049 - handlers.callback_handlers - INFO - Processing callback query from user 7691292720: joined
2025-07-21 17:26:27,046 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:26:30,696 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:26:34,468 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:27:16,829 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:27:20,997 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:27:24,051 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:27:25,453 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:27:27,338 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:27:30,144 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:27:33,016 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:27:37,615 - handlers.callback_handlers - INFO - Processing callback query from user **********: redeemGiftCode
2025-07-21 17:27:41,251 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 17:27:42,464 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:27:46,325 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:27:52,429 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:14,153 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:28:18,196 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:28:28,601 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:42,120 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: joined
2025-07-21 17:28:44,849 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:28:44,850 - __main__ - INFO - Shutting down bot...
2025-07-21 17:28:45,627 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:28:47,243 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: joined
2025-07-21 17:28:49,378 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:28:49,424 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:28:49,425 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:29:01,414 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:29:11,036 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:29:12,831 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:29:12,831 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:29:13,206 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:29:14,031 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:29:14,066 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:29:14,066 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:29:14,067 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:29:14,067 - __main__ - INFO - Starting bot polling...
2025-07-21 17:29:14,263 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:29:14,469 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:29:15,150 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:16,454 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:17,702 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:29:18,346 - services.user_service - INFO - Created new user: 1027113312 (Bala agency)
2025-07-21 17:29:21,068 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Bala agency (ID: 1027113312): success
2025-07-21 17:29:22,542 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: myWallet
2025-07-21 17:29:22,915 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user 7894202354
2025-07-21 17:29:24,536 - handlers.callback_handlers - INFO - Processing callback query from user 7894202354: customReferralLink
2025-07-21 17:29:24,906 - handlers.callback_handlers - WARNING - Ignoring expired query 'customReferralLink' from user 7894202354
2025-07-21 17:29:34,456 - handlers.callback_handlers - INFO - Processing callback query from user 1027113312: joined
2025-07-21 17:30:09,362 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:30:15,736 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:30:16,490 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:30:16,525 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:30:16,525 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:30:16,526 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:30:16,526 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:30:16,922 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:30:16,951 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:30:21,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_preview
2025-07-21 17:30:42,473 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:30:45,766 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:30:46,531 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:30:46,531 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 17:30:46,532 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:30:46,533 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:30:46,577 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:30:46,578 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:30:47,733 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2b347104627ee2037097'), 'user_id': **********, 'created_at': 1753099061, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753099061}
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:30:47,734 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:30:47,735 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:30:47,735 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:30:47,736 - handlers.session_handlers - INFO - Message text: 'Resart'
2025-07-21 17:30:49,343 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:30:50,114 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:30:50,161 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:30:50,658 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:30:56,714 - handlers.user_handlers - INFO - Custom referral parameter used: Resart -> User ID: **********
2025-07-21 17:30:59,943 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_buttons
2025-07-21 17:31:00,779 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:31:00,779 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'text': 'jkjhk', 'updated_at': 1753099246, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERVvVofiNBUqkp7jk8OME0EM8tfmCFjgACdMUxG7pq-Vd2FaAIzphr1QEAAwIAA3kAAzYE'}}
2025-07-21 17:31:00,780 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:31:00,780 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:31:00,830 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:31:00,831 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:31:02,201 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:31:04,395 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:31:05,148 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:31:05,191 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:31:05,192 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:31:05,588 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:31:07,928 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:31:11,485 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:32:17,398 - handlers.callback_handlers - INFO - Processing callback query from user 6494779610: myWallet
2025-07-21 17:32:40,105 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:32:43,226 - handlers.callback_handlers - INFO - Processing callback query from user 7880499409: joined
2025-07-21 17:32:49,036 - services.user_service - INFO - Created new user: 7456913321 (Naseem)
2025-07-21 17:32:52,414 - handlers.user_handlers - INFO - Notification sent to referrer 6020716948 about new user Naseem (ID: 7456913321): success
2025-07-21 17:32:54,531 - handlers.callback_handlers - INFO - Processing callback query from user 7880499409: myWallet
2025-07-21 17:33:34,477 - services.user_service - INFO - Created new user: ********** (Hashir)
2025-07-21 17:33:37,570 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Hashir (ID: **********): success
2025-07-21 17:33:46,717 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:34:05,177 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:34:45,839 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:35:09,448 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:35:15,139 - services.user_service - INFO - Created new user: ********** (Chan)
2025-07-21 17:35:18,242 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Chan (ID: **********): success
2025-07-21 17:35:19,842 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:35:21,334 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:35:22,756 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 17:35:24,208 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:35:25,669 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:35:28,812 - services.user_service - INFO - Created new user: ********* (Karthik)
2025-07-21 17:35:32,002 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Karthik (ID: *********): success
2025-07-21 17:35:33,705 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:35:35,858 - services.user_service - INFO - Created new user: ********** (Kuldip)
2025-07-21 17:35:38,961 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Kuldip (ID: **********): success
2025-07-21 17:35:40,566 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:35:43,621 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:35:49,307 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:35:58,518 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:36:06,404 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:36:08,051 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:10,002 - handlers.callback_handlers - INFO - Processing callback query from user *********: checkSubscription
2025-07-21 17:36:21,668 - handlers.callback_handlers - INFO - Processing callback query from user *********: joined
2025-07-21 17:36:25,858 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Karthik
2025-07-21 17:36:26,395 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:31,884 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: customReferralLink
2025-07-21 17:36:34,511 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:36:37,384 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:39,467 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:36:41,479 - handlers.callback_handlers - INFO - Processing callback query from user 7940369121: joined
2025-07-21 17:36:43,624 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7940369121, step: create_custom_referral ===
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2d477104627ee203709b'), 'user_id': 7940369121, 'created_at': 1753099592, 'data': {}, 'step': 'create_custom_referral', 'updated_at': 1753099592}
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:36:43,625 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:36:43,626 - handlers.session_handlers - INFO - Message text: 'https://t.me/InstantoPayBot?start=7940369121
I've Got Up To ₹100! Click URL To Join'
2025-07-21 17:36:47,466 - handlers.callback_handlers - INFO - Processing callback query from user *********: myWallet
2025-07-21 17:36:52,536 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 17:36:54,060 - handlers.callback_handlers - INFO - Processing callback query from user *********: extraRewards
2025-07-21 17:36:56,640 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:36:59,583 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:04,684 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:37:06,312 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:37:07,974 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:37:21,163 - handlers.callback_handlers - INFO - Processing callback query from user 6910817594: extraRewards
2025-07-21 17:37:24,504 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:37:38,919 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:37:43,702 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:45,810 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:37:47,751 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:37:50,584 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:37:52,570 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:37:54,485 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:37:55,284 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:37:59,649 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 17:38:03,512 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawal_method_bank
2025-07-21 17:38:09,407 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:38:22,454 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:38:31,735 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:38:38,322 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:39:06,735 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:39:10,620 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:39:15,388 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:39:20,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:39:25,999 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:39:32,789 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewTask_task_1752995334.fd08d118
2025-07-21 17:39:52,424 - handlers.callback_handlers - INFO - Processing callback query from user 7930384713: withdrawalRecord
2025-07-21 17:39:56,011 - handlers.callback_handlers - INFO - Processing callback query from user **********: submitTask_task_1752995334.fd08d118
2025-07-21 17:39:57,576 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e2e157104627ee203709d'), 'user_id': **********, 'created_at': 1753099798, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753099798}
2025-07-21 17:40:15,930 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:40:15,931 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 17:40:17,343 - services.task_service - INFO - Created new task submission: submission_1753099817.c3f1fbc2
2025-07-21 17:40:21,429 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:40:27,564 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:41:44,623 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: joined
2025-07-21 17:42:03,058 - services.user_service - INFO - Created new user: ********** (Mujahid)
2025-07-21 17:42:05,902 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mujahid (ID: **********): success
2025-07-21 17:42:08,716 - services.user_service - INFO - Created new user: ********** (Alone boy)
2025-07-21 17:42:11,467 - handlers.user_handlers - INFO - Notification sent to referrer 7683380744 about new user Alone boy (ID: **********): success
2025-07-21 17:42:14,402 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: checkSubscription
2025-07-21 17:42:19,270 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:42:19,270 - __main__ - INFO - Shutting down bot...
2025-07-21 17:42:19,881 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:42:19,928 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:42:19,929 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:42:26,287 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:42:35,283 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:42:36,965 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:42:36,965 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:42:37,358 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:42:38,222 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:42:38,265 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:42:38,267 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:42:38,267 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:42:38,267 - __main__ - INFO - Starting bot polling...
2025-07-21 17:42:38,466 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:42:38,665 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:42:40,848 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: joined
2025-07-21 17:42:43,376 - handlers.user_handlers - INFO - Custom referral parameter used: Vardaan568 -> User ID: 6398473258
2025-07-21 17:42:43,550 - services.user_service - INFO - Created new user: 6362857802 (N)
2025-07-21 17:42:46,219 - handlers.user_handlers - INFO - Notification sent to referrer 6398473258 about new user N (ID: 6362857802): success
2025-07-21 17:43:00,831 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: myWallet
2025-07-21 17:43:03,547 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:43:07,211 - services.referral_service - INFO - Referral reward processed: 7683380744 earned ₹3 for referring Alone boy
2025-07-21 17:43:26,583 - handlers.callback_handlers - INFO - Processing callback query from user 7671652874: verify_gift_broadcast_gift_broadcast_1753044284.16311b4f
2025-07-21 17:43:29,097 - services.user_service - INFO - Created new user: 6181831325 (Keerthi)
2025-07-21 17:43:31,902 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Keerthi (ID: 6181831325): success
2025-07-21 17:43:33,333 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: extraRewards
2025-07-21 17:43:42,741 - handlers.callback_handlers - INFO - Processing callback query from user 6181831325: joined
2025-07-21 17:43:45,987 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: myWallet
2025-07-21 17:43:51,242 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:43:52,008 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:43:52,046 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:43:52,047 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:43:52,047 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:43:52,047 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:43:52,750 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:43:52,750 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: cashOut
2025-07-21 17:43:54,515 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:43:56,467 - services.user_service - INFO - Created new user: 1650791981 (mohd)
2025-07-21 17:43:59,092 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user mohd (ID: 1650791981): success
2025-07-21 17:44:00,521 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:44:01,285 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:44:01,285 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 17:44:01,286 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:44:01,286 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:44:01,333 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:44:01,334 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:44:01,334 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:44:01,334 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:44:01,335 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:44:01,335 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:44:02,091 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:44:02,134 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:44:02,135 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:44:02,135 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:44:02,135 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:44:02,537 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:44:07,608 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: myWallet
2025-07-21 17:44:10,921 - handlers.callback_handlers - INFO - Processing callback query from user 6437087120: joined
2025-07-21 17:44:21,707 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:44:26,946 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:44:29,726 - handlers.callback_handlers - INFO - Processing callback query from user 1650791981: joined
2025-07-21 17:44:48,693 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:44:49,869 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:44:49,871 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 17:44:49,871 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:44:49,871 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:44:49,930 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:44:49,932 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:44:49,932 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:44:49,932 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:44:49,932 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:44:49,932 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:44:50,671 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:44:50,714 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:44:50,714 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:44:50,714 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:44:50,715 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:44:51,107 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:44:59,735 - handlers.callback_handlers - INFO - Processing callback query from user 1650791981: checkSubscription
2025-07-21 17:45:54,097 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:45:58,263 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Mujahid
2025-07-21 17:45:58,918 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:46:09,080 - handlers.callback_handlers - INFO - Processing callback query from user 7519233163: myWallet
2025-07-21 17:46:12,171 - handlers.callback_handlers - INFO - Processing callback query from user 7519233163: cashOut
2025-07-21 17:46:14,164 - handlers.callback_handlers - INFO - Processing callback query from user 7519233163: cashOut
2025-07-21 17:46:16,162 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 17:46:16,819 - services.user_service - INFO - Created new user: ********** (Protic)
2025-07-21 17:46:19,609 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Protic (ID: **********): success
2025-07-21 17:46:21,101 - handlers.callback_handlers - INFO - Processing callback query from user 7519233163: withdraw 100
2025-07-21 17:46:34,505 - handlers.callback_handlers - INFO - Processing callback query from user 7519233163: cashOut
2025-07-21 17:46:47,267 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:46:51,319 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Protic
2025-07-21 17:47:20,161 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:47:21,879 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:47:25,539 - services.referral_service - INFO - Referral reward processed: 7598365713 earned ₹4 for referring Yethihasoe
2025-07-21 17:47:26,066 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 17:47:28,085 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:47:31,479 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:47:32,978 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:47:35,818 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:47:39,566 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:47:42,136 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:47:47,177 - handlers.callback_handlers - INFO - Processing callback query from user **********: promotionReport
2025-07-21 17:47:53,249 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:48:01,587 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:48:03,339 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:48:09,799 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 17:48:13,116 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-21 17:48:17,598 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawal_method_usdt
2025-07-21 17:48:20,388 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:48:24,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:48:27,628 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:48:37,159 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-21 17:48:43,218 - handlers.callback_handlers - INFO - Processing callback query from user **********: set USDTAddress
2025-07-21 17:48:48,410 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:48:51,163 - handlers.callback_handlers - INFO - Processing callback query from user **********: redeemGiftCode
2025-07-21 17:49:06,357 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:49:10,602 - handlers.callback_handlers - INFO - Processing callback query from user **********: levelRewards
2025-07-21 17:49:16,431 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-21 17:49:21,194 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:49:29,110 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:50:03,510 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:50:24,806 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:50:28,777 - handlers.callback_handlers - INFO - Processing callback query from user **********: set name
2025-07-21 17:50:46,605 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-21 17:50:46,606 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e308d7104627ee20370a5'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-21 17:50:46,606 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:50:46,606 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:50:46,607 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:50:46,607 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:50:46,608 - handlers.session_handlers - INFO - Message text: 'Yethihasoe'
2025-07-21 17:50:55,422 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:51:06,147 - handlers.callback_handlers - INFO - Processing callback query from user **********: set ifsc
2025-07-21 17:51:16,416 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:51:20,206 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:51:23,483 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:51:25,591 - services.user_service - INFO - Created new user: ********** (Jitendra)
2025-07-21 17:51:28,516 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Jitendra (ID: **********): success
2025-07-21 17:51:30,033 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_withdrawal_100_usdt
2025-07-21 17:51:32,195 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753100492.755968dc
2025-07-21 17:51:33,920 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:51:36,748 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:51:49,148 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:51:52,856 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-21 17:51:52,856 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e30b37104627ee20370a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-21 17:51:52,857 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:51:52,857 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:51:52,857 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:51:52,857 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:51:52,857 - handlers.session_handlers - INFO - Message text: 'SBIN001980'
2025-07-21 17:52:21,711 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:52:29,743 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 17:52:34,026 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:52:36,750 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:52:40,577 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Jitendra
2025-07-21 17:52:42,775 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:52:44,535 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-21 17:52:44,536 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e30b37104627ee20370a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-21 17:52:44,536 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:52:44,536 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:52:44,536 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:52:44,537 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:52:44,537 - handlers.session_handlers - INFO - Message text: 'SBIN0001980'
2025-07-21 17:52:45,249 - utils.helpers - INFO - Cached bank details for IFSC: SBIN0001980
2025-07-21 17:52:52,738 - services.user_service - INFO - Created new user: ********** (Protic)
2025-07-21 17:52:55,554 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Protic (ID: **********): success
2025-07-21 17:52:58,138 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:53:02,815 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:53:06,706 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:53:08,499 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:53:28,820 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:53:34,252 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:53:39,018 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:53:42,340 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_SBIN0001980
2025-07-21 17:53:45,015 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:53:46,810 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:53:49,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:53:51,051 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-21 17:53:54,446 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:54:13,754 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 17:54:13,755 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e31577104627ee20370a7'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 17:54:13,756 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:54:13,756 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:54:13,756 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:54:13,756 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:54:13,756 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 17:54:18,081 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:54:21,139 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-21 17:54:27,123 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:54:30,991 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Protic
2025-07-21 17:54:31,598 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:54:54,535 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-21 17:54:54,535 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e31757104627ee20370a8'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-21 17:54:54,536 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:54:54,536 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:54:54,536 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:54:54,537 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:54:54,537 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-21 17:54:54,992 - services.withdrawal_service - INFO - Duplicate check for account **********: found 0 matches
2025-07-21 17:54:55,947 - services.withdrawal_service - INFO - Duplicate check for account **********: found 0 matches
2025-07-21 17:54:57,121 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:55:02,183 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:55:04,263 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:55:05,692 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 17:55:07,557 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:55:09,350 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:55:10,729 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 17:55:16,639 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-21 17:55:38,774 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:55:41,350 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:55:43,220 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:55:43,954 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:55:43,957 - services.broadcast_service - INFO - Data to save: {}
2025-07-21 17:55:43,957 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:55:43,957 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:55:44,003 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:55:44,004 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:55:44,004 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:55:44,004 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:55:44,004 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:55:44,004 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:55:44,814 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:55:44,858 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:55:44,858 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:55:44,859 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:55:44,859 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:55:45,248 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:55:45,346 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-21 17:55:45,347 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e31a57104627ee20370a9'), 'user_id': **********, 'created_at': 1753100710, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753100710}
2025-07-21 17:55:45,347 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:55:45,348 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:55:45,349 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:55:45,349 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:55:45,350 - handlers.session_handlers - INFO - Message text: '+959401071085'
2025-07-21 17:55:45,758 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +959401071085
2025-07-21 17:55:45,803 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-21 17:55:45,804 - services.withdrawal_service - INFO - Converted +959401071085 to API format: 959401071085
2025-07-21 17:55:45,805 - services.withdrawal_service - INFO - Sending OTP to +959401071085 (API format: 959401071085) using URL: https://sms.renflair.in/V1.php
2025-07-21 17:55:46,297 - services.withdrawal_service - INFO - OTP API response for +959401071085: Status 200, Content: {"message":"PHONE NUMBER INCORRECT FORMAT","status":"FAILED"}
2025-07-21 17:55:46,297 - services.withdrawal_service - INFO - OTP API analysis for +959401071085 (API format: 959401071085): Status=200, HasFailure=True, HasSuccess=False, ResponseLength=61
2025-07-21 17:55:46,297 - services.withdrawal_service - WARNING - OTP API returned 200 but response contains failure indicators: {"message":"PHONE NUMBER INCORRECT FORMAT","status":"FAILED"}
2025-07-21 17:55:46,297 - services.withdrawal_service - WARNING - This suggests API call succeeded but SMS delivery may have failed
2025-07-21 17:55:46,298 - handlers.session_handlers - ERROR - Failed to send OTP to +959401071085 for user **********
2025-07-21 17:55:47,768 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 17:55:47,769 - __main__ - INFO - Shutting down bot...
2025-07-21 17:55:51,473 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 17:55:51,518 - config.database - INFO - Disconnected from MongoDB
2025-07-21 17:55:51,520 - __main__ - INFO - Bot shutdown completed
2025-07-21 17:55:57,616 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 17:56:06,403 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 17:56:08,149 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 17:56:08,150 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 17:56:08,575 - __main__ - INFO - All handlers registered successfully
2025-07-21 17:56:10,621 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 17:56:10,665 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 17:56:10,665 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 17:56:10,665 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 17:56:10,665 - __main__ - INFO - Starting bot polling...
2025-07-21 17:56:11,475 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 17:56:11,686 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 17:56:14,270 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 17:56:14,717 - handlers.callback_handlers - WARNING - Ignoring expired query 'set mobile_number' from user **********
2025-07-21 17:56:14,718 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 17:56:19,028 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 17:56:24,552 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 17:56:26,804 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:56:27,590 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:56:27,634 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:56:27,634 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': 'Visit Website', 'url': 'https://example.com'}], [{'text': 'Join Channel', 'url': 'https://t.me/channel'}]]
2025-07-21 17:56:27,634 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:56:27,635 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:56:28,029 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:56:30,077 - handlers.callback_handlers - INFO - Processing callback query from user **********: reset_broadcast_all
2025-07-21 17:56:32,230 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_reset_all
2025-07-21 17:56:33,045 - services.broadcast_service - INFO - Completely clearing broadcast draft for admin **********
2025-07-21 17:56:33,093 - services.broadcast_service - INFO - Clear draft result for admin **********: acknowledged=True, modified=1
2025-07-21 17:56:33,920 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:56:33,964 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:56:33,965 - handlers.admin_handlers - INFO - Creating keyboard with 3 rows
2025-07-21 17:56:33,965 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:56:34,360 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:56:36,230 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_media
2025-07-21 17:56:45,704 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_media ===
2025-07-21 17:56:45,704 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e31fc7104627ee20370ab'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_media', 'updated_at': **********}
2025-07-21 17:56:45,704 - handlers.session_handlers - INFO - Message has text: False
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - === ROUTING TO BROADCAST MEDIA HANDLER for user ********** ===
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING START for user ********** ===
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message object exists: True
2025-07-21 17:56:45,705 - handlers.session_handlers - INFO - Message ID: 1136838
2025-07-21 17:56:45,706 - handlers.session_handlers - INFO - Message text: None
2025-07-21 17:56:45,706 - handlers.session_handlers - INFO - Message caption: None
2025-07-21 17:56:45,706 - handlers.session_handlers - INFO - === CHECKING MEDIA TYPES for user ********** ===
2025-07-21 17:56:45,706 - handlers.session_handlers - INFO - Message has photo: True
2025-07-21 17:56:45,707 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:56:45,707 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:56:45,707 - handlers.session_handlers - INFO - Message has audio: False
2025-07-21 17:56:45,707 - handlers.session_handlers - INFO - Message has voice: False
2025-07-21 17:56:45,707 - handlers.session_handlers - INFO - Message has sticker: False
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - Message has animation: False
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - Message has video_note: False
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - ✅ PHOTO detected - file_id: AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - === MEDIA PROCESSING SUCCESS for user ********** ===
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - Media data created: {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE'}
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - === SAVING TO BROADCAST DRAFT for user ********** ===
2025-07-21 17:56:45,708 - handlers.session_handlers - INFO - Getting existing broadcast draft for user **********
2025-07-21 17:56:45,745 - handlers.session_handlers - INFO - Existing draft: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'updated_at': 1753100793}
2025-07-21 17:56:45,745 - handlers.session_handlers - INFO - Updated draft with media: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'updated_at': 1753100793, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE'}}
2025-07-21 17:56:45,746 - handlers.session_handlers - INFO - Calling save_broadcast_draft for user **********
2025-07-21 17:56:45,746 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:56:45,746 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'updated_at': 1753100793, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE'}}
2025-07-21 17:56:45,746 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:56:45,746 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:56:45,793 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:56:45,793 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:56:45,794 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:56:45,794 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:56:45,794 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:56:45,794 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:56:45,794 - handlers.session_handlers - INFO - === BROADCAST DRAFT SAVE RESULT for user **********: True ===
2025-07-21 17:56:45,794 - handlers.session_handlers - INFO - Clearing session for user **********
2025-07-21 17:56:45,839 - handlers.session_handlers - INFO - Session cleared for user **********
2025-07-21 17:56:45,839 - handlers.session_handlers - INFO - === SENDING SUCCESS MESSAGE to user ********** ===
2025-07-21 17:56:46,680 - handlers.session_handlers - INFO - ✅ SUCCESS MESSAGE SENT to user **********
2025-07-21 17:56:46,680 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING END for user ********** ===
2025-07-21 17:56:48,349 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:56:49,112 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:56:49,154 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:56:49,155 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 17:56:49,156 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:56:49,635 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:56:53,836 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_text
2025-07-21 17:56:56,520 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_text ===
2025-07-21 17:56:56,521 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e320e7104627ee20370ad'), 'user_id': **********, 'created_at': 1753100815, 'data': {}, 'step': 'broadcast_text', 'updated_at': 1753100815}
2025-07-21 17:56:56,521 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:56:56,521 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:56:56,521 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:56:56,521 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:56:56,522 - handlers.session_handlers - INFO - Message text: '<b>🎉Earn Up to ₹100 Per Referral
Invite others to join and unlock instant rewards directly in your wallet.
🎁The more you refer, the more you earn.

☎️ Need help? Contact @instantohelpbot</b>'
2025-07-21 17:56:56,564 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:56:56,565 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'updated_at': 1753100805, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE'}, 'text': '<b>🎉Earn Up to ₹100 Per Referral\nInvite others to join and unlock instant rewards directly in your wallet.\n🎁The more you refer, the more you earn.\n\n☎️ Need help? Contact @instantohelpbot</b>'}
2025-07-21 17:56:56,566 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:56:56,567 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:56:56,615 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:56:56,616 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:56:56,616 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:56:56,617 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:56:56,617 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:56:56,617 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:56:58,137 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:56:58,952 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:56:58,998 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:56:58,999 - handlers.admin_handlers - INFO - Creating keyboard with 5 rows
2025-07-21 17:56:58,999 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:56:59,400 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:56:59,401 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 17:57:12,132 - handlers.callback_handlers - INFO - Processing callback query from user **********: broadcast_buttons
2025-07-21 17:57:18,931 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-21 17:57:18,932 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e32217104627ee20370ae'), 'user_id': **********, 'created_at': 1753100833, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1753100833}
2025-07-21 17:57:18,932 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 17:57:18,933 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 17:57:18,933 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 17:57:18,933 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 17:57:18,934 - handlers.session_handlers - INFO - Message text: '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 | https://t.me/InstantoPayBot?start=from_channel'
2025-07-21 17:57:18,978 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-21 17:57:18,979 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('687e22ff7104627ee2037085'), 'admin_id': **********, 'status': 'draft', 'updated_at': 1753100816, 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAERWMZofjIE0JCDFX0Y9gwL20kKpDfwcQAClsUxG7pq-Ve38iUHluvDfQEAAwIAA3kAAzYE'}, 'text': '<b>🎉Earn Up to ₹100 Per Referral\nInvite others to join and unlock instant rewards directly in your wallet.\n🎁The more you refer, the more you earn.\n\n☎️ Need help? Contact @instantohelpbot</b>', 'buttons': [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]}
2025-07-21 17:57:18,979 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['ac-bac3qqk-shard-00-02.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-00.egi1fti.mongodb.net:27017', 'ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-zbn8ur-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-21 17:57:18,980 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-21 17:57:19,033 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-21 17:57:19,033 - services.broadcast_service - INFO - Acknowledged: True
2025-07-21 17:57:19,033 - services.broadcast_service - INFO - Matched count: 1
2025-07-21 17:57:19,034 - services.broadcast_service - INFO - Modified count: 1
2025-07-21 17:57:19,034 - services.broadcast_service - INFO - Upserted ID: None
2025-07-21 17:57:19,034 - services.broadcast_service - INFO - Returning success: True
2025-07-21 17:57:21,281 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_broadcast
2025-07-21 17:57:22,040 - handlers.admin_handlers - INFO - Getting broadcast draft for admin **********
2025-07-21 17:57:22,087 - handlers.admin_handlers - INFO - Retrieved draft for admin **********: True
2025-07-21 17:57:22,088 - handlers.admin_handlers - INFO - Processing buttons: [[{'text': '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔', 'url': 'https://t.me/InstantoPayBot?start=from_channel'}]]
2025-07-21 17:57:22,089 - handlers.admin_handlers - INFO - Creating keyboard with 6 rows
2025-07-21 17:57:22,089 - handlers.admin_handlers - INFO - Keyboard created successfully
2025-07-21 17:57:22,477 - handlers.admin_handlers - INFO - Message edited successfully
2025-07-21 17:58:15,078 - handlers.callback_handlers - INFO - Processing callback query from user **********: start_broadcast
2025-07-21 17:58:24,390 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_broadcast
2025-07-21 17:58:26,762 - config.broadcast_config - INFO - Selected large config for 54819 users: 300.0 msg/sec, ~3.05 minutes
2025-07-21 17:58:26,762 - services.broadcast_service - INFO - Starting optimized broadcast broadcast_0bc1333e810a to 54819 users
2025-07-21 17:58:26,878 - services.broadcast_service - ERROR - Error in concurrent batch processing: name 'performance_monitor' is not defined
2025-07-21 17:58:26,879 - services.broadcast_service - ERROR - Error executing optimized broadcast broadcast_0bc1333e810a: name 'current_batch' is not defined
2025-07-21 17:59:08,905 - handlers.callback_handlers - INFO - Processing callback query from user **********: approve_withdrawal_**********
2025-07-21 17:59:12,990 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_approve_withdrawal_**********
2025-07-21 17:59:15,652 - services.withdrawal_service - INFO - Withdrawal approved: User **********, Amount ₹100
2025-07-21 17:59:16,444 - handlers.admin_handlers - INFO - Admin ********** (Kêviñ) approved withdrawal for user ********** (₹100)
2025-07-21 17:59:22,843 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewPendingWithdrawals
2025-07-21 17:59:24,391 - handlers.admin_handlers - INFO - Loaded 6 pending withdrawals in 0.415s
2025-07-21 17:59:24,786 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.810s
2025-07-21 17:59:37,415 - handlers.callback_handlers - INFO - Processing callback query from user **********: failUserWithdrawal
2025-07-21 17:59:41,308 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_**********
2025-07-21 17:59:41,775 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 17:59:42,781 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-21 17:59:43,247 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 17:59:43,300 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-21 17:59:43,395 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 17:59:43,435 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 17:59:44,270 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 17:59:44,270 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 17:59:45,027 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-21 18:00:00,954 - handlers.callback_handlers - INFO - Processing callback query from user **********: manage_withdrawals
2025-07-21 18:00:05,226 - handlers.callback_handlers - INFO - Processing callback query from user **********: failUserWithdrawal
2025-07-21 18:00:12,065 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_**********
2025-07-21 18:00:12,876 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 18:00:13,840 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹200
2025-07-21 18:00:14,301 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 18:00:14,450 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹200
2025-07-21 18:00:14,551 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 18:00:14,601 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 18:00:15,562 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 18:00:15,562 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:00:16,354 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:00:20,997 - handlers.callback_handlers - INFO - Processing callback query from user **********: manage_withdrawals
2025-07-21 18:00:29,188 - handlers.callback_handlers - INFO - Processing callback query from user **********: viewPendingWithdrawals
2025-07-21 18:00:30,769 - handlers.admin_handlers - INFO - Loaded 4 pending withdrawals in 0.426s
2025-07-21 18:00:31,175 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.833s
2025-07-21 18:00:50,052 - handlers.callback_handlers - INFO - Processing callback query from user **********: failUserWithdrawal
2025-07-21 18:01:00,501 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_7596351024
2025-07-21 18:01:01,275 - handlers.callback_handlers - INFO - Processing withdrawal reject for user 7596351024 by admin **********
2025-07-21 18:01:02,260 - handlers.callback_handlers - INFO - Processing reject for user 7596351024, amount: ₹100
2025-07-21 18:01:02,664 - services.withdrawal_service - INFO - Starting withdrawal rejection for user 7596351024 by admin **********
2025-07-21 18:01:02,709 - services.withdrawal_service - INFO - Rejecting withdrawal: User 7596351024, Amount ₹100
2025-07-21 18:01:02,809 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User 7596351024
2025-07-21 18:01:02,857 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user 7596351024
2025-07-21 18:01:03,864 - services.withdrawal_service - INFO - Successfully sent rejection notification to user 7596351024
2025-07-21 18:01:03,865 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User 7596351024, Amount ₹100
2025-07-21 18:01:04,616 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User 7596351024, Amount ₹100
2025-07-21 18:01:05,524 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 18:01:51,694 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:01:55,823 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:01:59,512 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 200
2025-07-21 18:02:02,906 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_withdrawal_200_
2025-07-21 18:02:04,980 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753101124.9ca484bf
2025-07-21 18:02:39,435 - handlers.callback_handlers - INFO - Processing callback query from user **********: bot_maintenance
2025-07-21 18:02:45,692 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 18:02:51,046 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:02:54,051 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:02:58,000 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 18:02:59,825 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 18:03:02,847 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:03:04,731 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:03:08,643 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:03:13,657 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 18:03:15,597 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:03:16,960 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:03:24,343 - services.user_service - INFO - Created new user: ********** (Aish)
2025-07-21 18:03:28,394 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Aish (ID: **********): success
2025-07-21 18:03:38,273 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:03:39,811 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 200
2025-07-21 18:03:42,789 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 18:03:49,437 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 18:03:50,867 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_**********
2025-07-21 18:03:51,433 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 18:03:53,238 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹200
2025-07-21 18:03:54,000 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 18:03:54,133 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹200
2025-07-21 18:03:54,237 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 18:03:54,281 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 18:03:55,666 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 18:03:55,667 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:03:56,445 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:04:09,663 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:04:20,620 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:04:28,822 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 18:04:35,365 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:04:38,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:04:41,305 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 200
2025-07-21 18:04:44,992 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_withdrawal_200_
2025-07-21 18:04:47,227 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753101287.ab5ccfff
2025-07-21 18:04:51,419 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 18:04:51,419 - __main__ - INFO - Shutting down bot...
2025-07-21 18:04:52,139 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 18:04:52,191 - config.database - INFO - Disconnected from MongoDB
2025-07-21 18:04:52,191 - __main__ - INFO - Bot shutdown completed
2025-07-21 18:05:21,076 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 18:05:30,234 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 18:05:32,008 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 18:05:32,009 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 18:05:32,506 - __main__ - INFO - All handlers registered successfully
2025-07-21 18:05:33,458 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 18:05:33,501 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 18:05:33,501 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 18:05:33,501 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 18:05:33,502 - __main__ - INFO - Starting bot polling...
2025-07-21 18:05:33,710 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 18:05:33,919 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 18:05:48,080 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 18:05:50,272 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_**********
2025-07-21 18:05:50,666 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 18:05:51,760 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹200
2025-07-21 18:05:52,197 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-21 18:05:52,279 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹200
2025-07-21 18:05:52,370 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-21 18:05:52,433 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-21 18:05:53,306 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-21 18:05:53,306 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:05:54,217 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹200
2025-07-21 18:06:05,908 - services.user_service - INFO - Created new user: ********** (Abhay)
2025-07-21 18:06:08,884 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Abhay (ID: **********): success
2025-07-21 18:06:14,550 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 18:06:32,173 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-21 18:06:38,983 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-21 18:06:42,595 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Abhay
2025-07-21 18:06:53,462 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:06:58,043 - handlers.callback_handlers - INFO - Processing callback query from user 6003547946: joined
2025-07-21 18:07:00,700 - handlers.callback_handlers - INFO - Processing callback query from user 7133667536: joined
2025-07-21 18:07:03,993 - services.user_service - INFO - Created new user: ********** (Himanshu)
2025-07-21 18:07:06,774 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Himanshu (ID: **********): success
2025-07-21 18:07:08,482 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:07:18,225 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 1000
2025-07-21 18:07:24,995 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 18:07:31,293 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-21 18:08:00,864 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:00,865 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:07,372 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:07,373 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:15,252 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:15,256 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:22,377 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:22,378 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:33,763 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:33,764 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:42,048 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_withdrawal_100_
2025-07-21 18:08:48,627 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:48,628 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:50,354 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:08:52,239 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 18:08:56,595 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:08:56,596 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:03,750 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:03,750 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:09,764 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:09,765 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:18,947 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:18,948 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:09:24,665 - handlers.callback_handlers - INFO - Processing callback query from user **********: reject_withdrawal_**********
2025-07-21 18:09:24,898 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 18:09:24,898 - __main__ - INFO - Shutting down bot...
2025-07-21 18:09:25,469 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-21 18:09:26,548 - handlers.callback_handlers - WARNING - No pending withdrawal for user ********** (amount: 0)
2025-07-21 18:09:27,113 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 18:09:27,154 - config.database - INFO - Disconnected from MongoDB
2025-07-21 18:09:27,155 - __main__ - INFO - Bot shutdown completed
2025-07-21 18:11:07,782 - __main__ - INFO - Connecting to MongoDB...
2025-07-21 18:11:17,522 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-21 18:11:19,395 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-21 18:11:19,395 - __main__ - INFO - Initializing Telegram bot...
2025-07-21 18:11:19,832 - __main__ - INFO - All handlers registered successfully
2025-07-21 18:11:20,955 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-21 18:11:20,999 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-21 18:11:20,999 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-21 18:11:21,000 - __main__ - INFO - Bot initialization completed successfully
2025-07-21 18:11:21,000 - __main__ - INFO - Starting bot polling...
2025-07-21 18:11:21,211 - apscheduler.scheduler - INFO - Scheduler started
2025-07-21 18:11:21,419 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-21 18:11:26,693 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:26,693 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:33,888 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:33,889 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:42,205 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:42,206 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:49,362 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:49,363 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:55,358 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:11:55,358 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:04,712 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:04,712 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:11,183 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-21 18:12:15,257 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-21 18:12:18,661 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:18,663 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:20,873 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-21 18:12:28,084 - handlers.callback_handlers - INFO - Processing callback query from user **********: changeWithdrawalMethod
2025-07-21 18:12:33,554 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawal_method_bank
2025-07-21 18:12:36,557 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:36,557 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 18:12:38,263 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-21 18:12:38,988 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 18:12:39,208 - __main__ - INFO - Shutting down bot...
2025-07-21 18:12:39,733 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-22 10:20:29,116 - __main__ - INFO - Connecting to MongoDB...
2025-07-22 10:20:37,946 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-22 10:20:39,917 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-22 10:20:39,917 - __main__ - INFO - Initializing Telegram bot...
2025-07-22 10:20:40,275 - __main__ - INFO - All handlers registered successfully
2025-07-22 10:20:41,181 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-22 10:20:41,224 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-22 10:20:41,224 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-22 10:20:41,224 - __main__ - INFO - Bot initialization completed successfully
2025-07-22 10:20:41,224 - __main__ - INFO - Starting bot polling...
2025-07-22 10:20:41,429 - apscheduler.scheduler - INFO - Scheduler started
2025-07-22 10:20:41,634 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-22 10:20:42,362 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:20:42,362 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': 1753159815, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753159815}
2025-07-22 10:20:42,363 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:20:42,363 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:20:42,363 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:20:42,363 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:20:42,364 - handlers.session_handlers - INFO - Message text: '1202'
2025-07-22 10:20:43,240 - services.user_service - INFO - Created new user: ********** (.)
2025-07-22 10:20:45,805 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user . (ID: **********): success
2025-07-22 10:20:47,510 - services.user_service - INFO - Created new user: ********** (Singh)
2025-07-22 10:20:50,089 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Singh (ID: **********): success
2025-07-22 10:20:51,490 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:20:54,988 - services.referral_service - INFO - Referral reward processed: 64******** earned ₹1 for referring ✌️Yescoiner
2025-07-22 10:20:59,285 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:21:01,886 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:21:06,015 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:21:09,673 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Kamsy🌱SEED
2025-07-22 10:21:10,260 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:21:10,261 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-22 10:21:10,261 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:21:10,261 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:21:10,262 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:21:10,262 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:21:10,262 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-22 10:21:10,751 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-22 10:21:10,792 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-22 10:21:10,793 - services.withdrawal_service - INFO - Converted +************ to API format: 7398453958
2025-07-22 10:21:10,793 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 7398453958) using URL: https://sms.renflair.in/V1.php
2025-07-22 10:21:13,062 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"bVs6eNm4nxOzocw","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-22 10:21:13,063 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 7398453958): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-22 10:21:13,064 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 6053
2025-07-22 10:21:13,064 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-22 10:21:13,504 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-22 10:21:13,939 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:21:15,319 - handlers.callback_handlers - INFO - Processing callback query from user 64********: extraRewards
2025-07-22 10:21:16,506 - handlers.callback_handlers - INFO - Processing callback query from user **********: customref_list_page_2
2025-07-22 10:21:17,742 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:21:21,217 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring .
2025-07-22 10:21:21,738 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:21:22,991 - handlers.callback_handlers - INFO - Processing callback query from user **********: customref_list_page_1
2025-07-22 10:21:24,161 - handlers.callback_handlers - INFO - Processing callback query from user 64********: redeemGiftCode
2025-07-22 10:21:25,497 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:21:25,497 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': **********, 'data': {'mobile_number': '+************', 'otp': '6053', 'otp_timestamp': **********}, 'step': 'verify_otp', 'updated_at': **********}
2025-07-22 10:21:25,497 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:21:25,497 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:21:25,497 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:21:25,498 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:21:25,498 - handlers.session_handlers - INFO - Message text: '2586'
2025-07-22 10:21:26,307 - handlers.callback_handlers - INFO - Processing callback query from user 64********: levelRewards
2025-07-22 10:21:27,568 - handlers.callback_handlers - INFO - Processing callback query from user **********: customref_page_info
2025-07-22 10:21:29,760 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:21:37,061 - handlers.callback_handlers - INFO - Processing callback query from user **********: customref_help
2025-07-22 10:21:38,751 - handlers.callback_handlers - INFO - Processing callback query from user 64********: extraRewards
2025-07-22 10:21:40,054 - handlers.callback_handlers - INFO - Processing callback query from user **********: set ifsc
2025-07-22 10:21:44,350 - handlers.callback_handlers - INFO - Processing callback query from user 64********: taskRewards
2025-07-22 10:21:47,911 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:21:49,408 - services.rank_service - INFO - Retrieved withdrawal leaderboard with 10 users
2025-07-22 10:21:50,934 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:21:53,415 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': 1753159909, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753159909}
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:21:53,416 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-22 10:21:53,788 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-22 10:21:53,830 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-22 10:21:53,831 - services.withdrawal_service - INFO - Converted +************ to API format: 7398453958
2025-07-22 10:21:53,831 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 7398453958) using URL: https://sms.renflair.in/V1.php
2025-07-22 10:21:55,917 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"ZoMXRwf9NVIv3at","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-22 10:21:55,918 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 7398453958): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-22 10:21:55,918 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 7854
2025-07-22 10:21:55,918 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-22 10:21:56,366 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-22 10:21:58,773 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-22 10:22:03,677 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:22:07,691 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:22:11,405 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Singh
2025-07-22 10:22:12,010 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:22:25,292 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:22:31,715 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:22:33,780 - services.rank_service - INFO - Retrieved withdrawal leaderboard with 10 users
2025-07-22 10:22:44,339 - services.rank_service - INFO - Retrieved referral leaderboard with 10 users
2025-07-22 10:22:46,655 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:22:53,236 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-22 10:23:36,513 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:23:36,513 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': 1753159915, 'data': {'mobile_number': '+************', 'otp': '7854', 'otp_timestamp': 1753159915}, 'step': 'verify_otp', 'updated_at': 1753159915}
2025-07-22 10:23:36,514 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:23:36,514 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:23:36,514 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:23:36,514 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:23:36,514 - handlers.session_handlers - INFO - Message text: '1979'
2025-07-22 10:23:41,318 - handlers.callback_handlers - INFO - Processing callback query from user **********: resend_otp
2025-07-22 10:23:41,685 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'resend_otp': 'CallbackHandlers' object has no attribute 'session_handlers'
2025-07-22 10:23:41,685 - handlers.callback_handlers - ERROR - Error in callback handler for data 'resend_otp': 'CallbackHandlers' object has no attribute 'session_handlers'
2025-07-22 10:23:47,192 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:23:51,591 - services.user_service - INFO - Created new user: ********** (puneeth)
2025-07-22 10:23:54,369 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user puneeth (ID: **********): success
2025-07-22 10:23:58,067 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:23:59,401 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:24:04,100 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:24:06,889 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:24:06,891 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-22 10:24:06,891 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:24:06,891 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:24:06,891 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:24:06,893 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:24:06,893 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-22 10:24:07,343 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-22 10:24:07,382 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-22 10:24:07,383 - services.withdrawal_service - INFO - Converted +************ to API format: 7398453958
2025-07-22 10:24:07,383 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 7398453958) using URL: https://sms.renflair.in/V1.php
2025-07-22 10:24:09,756 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"v8WPRyGLKxOzIe4","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-22 10:24:09,756 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 7398453958): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-22 10:24:09,757 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 7699
2025-07-22 10:24:09,757 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-22 10:24:10,199 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-22 10:24:10,200 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:24:16,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 1000
2025-07-22 10:24:22,539 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:24:22,539 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': 1753160049, 'data': {'mobile_number': '+************', 'otp': '7699', 'otp_timestamp': 1753160049}, 'step': 'verify_otp', 'updated_at': 1753160049}
2025-07-22 10:24:22,540 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:24:22,540 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:24:22,540 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:24:22,540 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:24:22,540 - handlers.session_handlers - INFO - Message text: '6196'
2025-07-22 10:24:28,588 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:24:31,155 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:24:32,649 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:24:37,116 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:24:38,497 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-22 10:24:41,979 - services.user_service - INFO - Created new user: ********** (Karan)
2025-07-22 10:24:45,151 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Karan (ID: **********): success
2025-07-22 10:24:46,719 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:24:48,701 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:24:52,467 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring puneeth
2025-07-22 10:24:55,729 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:24:58,687 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-22 10:24:58,687 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-22 10:24:58,688 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:24:58,688 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:24:58,688 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:24:58,689 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:24:58,689 - handlers.session_handlers - INFO - Message text: 'BABR0SHYAMX'
2025-07-22 10:24:59,101 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:25:03,650 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-22 10:25:05,097 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:25:06,647 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-22 10:25:08,505 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:25:09,877 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-22 10:25:10,285 - handlers.callback_handlers - INFO - Processing callback query from user **********: bot_statistics
2025-07-22 10:25:11,783 - services.user_service - INFO - Created new user: ********** (Michael)
2025-07-22 10:25:14,499 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Michael (ID: **********): success
2025-07-22 10:25:16,040 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-22 10:25:17,428 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_history
2025-07-22 10:25:18,662 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:25:20,693 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:25:22,721 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:25:24,429 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-22 10:25:24,827 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:25:28,826 - handlers.callback_handlers - INFO - Processing callback query from user **********: history_custom_link_usage_1
2025-07-22 10:25:30,163 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f16527104627ee20372a6'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:25:30,165 - handlers.session_handlers - INFO - Message text: '**************'
2025-07-22 10:25:30,647 - services.withdrawal_service - INFO - Duplicate check for account **************: found 0 matches
2025-07-22 10:25:31,646 - services.withdrawal_service - INFO - Duplicate check for account **************: found 0 matches
2025-07-22 10:25:32,384 - services.user_service - INFO - Created new user: ********** (Aman💖)
2025-07-22 10:25:35,128 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Aman💖 (ID: **********): success
2025-07-22 10:25:36,643 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-22 10:25:38,535 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:25:42,138 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Karan
2025-07-22 10:25:42,728 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:25:44,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:25:47,717 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:25:50,252 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:25:51,996 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:25:53,893 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-22 10:25:55,720 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:25:55,721 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f19d77104627ee20372d9'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-22 10:25:55,721 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:25:55,721 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:25:55,722 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:25:55,722 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:25:55,722 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-22 10:25:56,385 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-22 10:25:56,428 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-22 10:25:56,428 - services.withdrawal_service - INFO - Converted +************ to API format: 7398453958
2025-07-22 10:25:56,429 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 7398453958) using URL: https://sms.renflair.in/V1.php
2025-07-22 10:25:58,679 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"XaCKoZWP56vpLFs","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-22 10:25:58,679 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 7398453958): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-22 10:25:58,680 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 3055
2025-07-22 10:25:58,680 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-22 10:25:59,165 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-22 10:25:59,167 - handlers.callback_handlers - INFO - Processing callback query from user **********: taskRewards
2025-07-22 10:26:00,974 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:26:05,765 - handlers.callback_handlers - INFO - Processing callback query from user **********: extraRewards
2025-07-22 10:26:18,701 - handlers.callback_handlers - INFO - Processing callback query from user **********: user_history
2025-07-22 10:26:20,728 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:26:20,728 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f19d77104627ee20372d9'), 'user_id': **********, 'created_at': 1753160158, 'data': {'mobile_number': '+************', 'otp': '3055', 'otp_timestamp': 1753160158}, 'step': 'verify_otp', 'updated_at': 1753160158}
2025-07-22 10:26:20,728 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:26:20,730 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:26:20,730 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:26:20,730 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:26:20,730 - handlers.session_handlers - INFO - Message text: '6053'
2025-07-22 10:26:28,480 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:26:28,482 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f19d77104627ee20372d9'), 'user_id': **********, 'created_at': 1753160158, 'data': {'mobile_number': '+************', 'otp': '3055', 'otp_timestamp': 1753160158}, 'step': 'verify_otp', 'updated_at': 1753160158}
2025-07-22 10:26:28,483 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:26:28,483 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:26:28,485 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:26:28,485 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:26:28,486 - handlers.session_handlers - INFO - Message text: '7854'
2025-07-22 10:26:31,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:26:40,794 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:26:44,104 - services.user_service - INFO - Created new user: ********** (Cengis)
2025-07-22 10:26:48,765 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Cengis (ID: **********): success
2025-07-22 10:26:51,313 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:26:59,598 - handlers.callback_handlers - INFO - Processing callback query from user **********: set mobile_number
2025-07-22 10:27:01,064 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:27:03,776 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:27:05,664 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-22 10:27:05,665 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f19d77104627ee20372d9'), 'user_id': **********, 'created_at': 1753160221, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753160221}
2025-07-22 10:27:05,665 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:27:05,665 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:27:05,665 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:27:05,665 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:27:05,666 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-22 10:27:06,117 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-22 10:27:06,164 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-22 10:27:06,165 - services.withdrawal_service - INFO - Converted +************ to API format: 7398453958
2025-07-22 10:27:06,165 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 7398453958) using URL: https://sms.renflair.in/V1.php
2025-07-22 10:27:08,507 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"rbIjg4OkZ8qoiQf","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-22 10:27:08,507 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 7398453958): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-22 10:27:08,508 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 5168
2025-07-22 10:27:08,508 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-22 10:27:08,954 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-22 10:27:29,279 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-22 10:27:29,279 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f19d77104627ee20372d9'), 'user_id': **********, 'created_at': 1753160228, 'data': {'mobile_number': '+************', 'otp': '5168', 'otp_timestamp': 1753160228}, 'step': 'verify_otp', 'updated_at': 1753160228}
2025-07-22 10:27:29,280 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:27:29,280 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:27:29,280 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:27:29,280 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:27:29,280 - handlers.session_handlers - INFO - Message text: '7699'
2025-07-22 10:27:34,342 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:27:37,937 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Cengis
2025-07-22 10:27:37,982 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-22 10:27:37,982 - __main__ - INFO - Shutting down bot...
2025-07-22 10:27:38,445 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:27:38,982 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-22 10:27:40,307 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-22 10:27:40,353 - config.database - INFO - Disconnected from MongoDB
2025-07-22 10:27:40,354 - __main__ - INFO - Bot shutdown completed
2025-07-22 10:32:14,072 - __main__ - INFO - Connecting to MongoDB...
2025-07-22 10:32:24,404 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-22 10:32:26,143 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-22 10:32:26,143 - __main__ - INFO - Initializing Telegram bot...
2025-07-22 10:32:26,690 - __main__ - INFO - All handlers registered successfully
2025-07-22 10:32:27,579 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-22 10:32:27,621 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-22 10:32:27,622 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-22 10:32:27,622 - __main__ - INFO - Bot initialization completed successfully
2025-07-22 10:32:27,622 - __main__ - INFO - Starting bot polling...
2025-07-22 10:32:27,821 - apscheduler.scheduler - INFO - Scheduler started
2025-07-22 10:32:28,020 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-22 10:32:31,566 - services.user_service - INFO - Created new user: 6462307633 (Signature mind your dad)
2025-07-22 10:32:34,447 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Signature mind your dad (ID: 6462307633): success
2025-07-22 10:32:36,390 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-22 10:32:37,570 - handlers.user_handlers - ERROR - Error in handle_start_command: Forbidden: bot was blocked by the user
2025-07-22 10:32:37,937 - __main__ - ERROR - Error in start handler: Forbidden: bot was blocked by the user
2025-07-22 10:32:38,590 - services.user_service - INFO - Created new user: ********** (Shweta)
2025-07-22 10:32:41,660 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Shweta (ID: **********): success
2025-07-22 10:32:43,556 - services.user_service - INFO - Created new user: ********** (Anurag)
2025-07-22 10:32:46,567 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Anurag (ID: **********): success
2025-07-22 10:32:50,264 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-22 10:32:53,836 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-22 10:32:57,177 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-22 10:32:59,071 - services.user_service - INFO - Created new user: 5801984139 (.)
2025-07-22 10:33:01,834 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user . (ID: 5801984139): success
2025-07-22 10:33:10,816 - services.user_service - INFO - Created new user: 5281693923 (Riju)
2025-07-22 10:33:13,842 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Riju (ID: 5281693923): success
2025-07-22 10:33:15,416 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:33:15,778 - handlers.callback_handlers - WARNING - Ignoring expired query 'myWallet' from user **********
2025-07-22 10:33:24,094 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:33:27,646 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:33:31,183 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:33:35,134 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 200
2025-07-22 10:33:43,558 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:33:49,638 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:33:52,006 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_withdrawal_200_
2025-07-22 10:33:54,497 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753160634.d44d5bdf
2025-07-22 10:33:56,208 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:34:02,216 - handlers.callback_handlers - INFO - Processing callback query from user 5281693923: joined
2025-07-22 10:34:05,901 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Riju
2025-07-22 10:34:08,132 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:34:12,295 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-22 10:34:15,625 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:34:17,820 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:34:19,622 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-22 10:34:23,421 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:34:26,417 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-22 10:34:41,242 - handlers.callback_handlers - INFO - Processing callback query from user **********: bot_statistics
2025-07-22 10:34:44,578 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:34:47,071 - handlers.callback_handlers - INFO - Processing callback query from user **********: overall_statistics
2025-07-22 10:34:51,535 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:34:55,868 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:34:59,535 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Anurag
2025-07-22 10:35:01,913 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:35:04,653 - handlers.callback_handlers - INFO - Processing callback query from user **********: daily_analytics
2025-07-22 10:35:05,759 - handlers.callback_handlers - INFO - Processing callback query from user **********: checkSubscription
2025-07-22 10:35:15,665 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:35:19,236 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:35:22,946 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Shweta
2025-07-22 10:35:23,476 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:35:28,908 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:35:30,328 - handlers.callback_handlers - INFO - Processing callback query from user **********: daily_analytics_yesterday
2025-07-22 10:35:33,205 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdrawalRecord
2025-07-22 10:35:37,715 - handlers.callback_handlers - INFO - Processing callback query from user **********: cashOut
2025-07-22 10:35:41,951 - handlers.callback_handlers - INFO - Processing callback query from user **********: withdraw 100
2025-07-22 10:35:46,508 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:35:48,004 - handlers.callback_handlers - INFO - Processing callback query from user **********: customReferralLink
2025-07-22 10:35:49,570 - handlers.callback_handlers - INFO - Processing callback query from user **********: set name
2025-07-22 10:35:55,502 - handlers.callback_handlers - INFO - Processing callback query from user **********: myWallet
2025-07-22 10:35:56,927 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-22 10:35:56,927 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f1c2e7104627ee20372dd'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-22 10:35:56,927 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:35:56,927 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:35:56,928 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:35:56,928 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:35:56,928 - handlers.session_handlers - INFO - Message text: 'Sarita Pandey'
2025-07-22 10:36:00,812 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:36:02,624 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:36:05,006 - handlers.callback_handlers - INFO - Processing callback query from user **********: set ifsc
2025-07-22 10:36:18,540 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-22 10:36:18,541 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f1c3e7104627ee20372de'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-22 10:36:18,541 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:36:18,542 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:36:18,542 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:36:18,542 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:36:18,543 - handlers.session_handlers - INFO - Message text: 'BARB0SHYAMX'
2025-07-22 10:36:19,425 - utils.helpers - INFO - Cached bank details for IFSC: BARB0SHYAMX
2025-07-22 10:36:23,266 - handlers.callback_handlers - INFO - Processing callback query from user **********: confirm_ifsc_BARB0SHYAMX
2025-07-22 10:36:26,625 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:36:28,587 - services.user_service - INFO - Created new user: ********** (shyna)
2025-07-22 10:36:33,536 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user shyna (ID: **********): success
2025-07-22 10:36:35,484 - handlers.callback_handlers - INFO - Processing callback query from user **********: set email
2025-07-22 10:36:37,307 - services.user_service - INFO - Created new user: ********** (Sibi)
2025-07-22 10:36:40,509 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sibi (ID: **********): success
2025-07-22 10:36:42,442 - services.user_service - INFO - Created new user: ********** (Tejaswi)
2025-07-22 10:36:45,448 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Tejaswi (ID: **********): success
2025-07-22 10:36:48,772 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-22 10:36:48,772 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687f1c5c7104627ee20372df'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-22 10:36:48,773 - handlers.session_handlers - INFO - Message has text: True
2025-07-22 10:36:48,774 - handlers.session_handlers - INFO - Message has photo: False
2025-07-22 10:36:48,774 - handlers.session_handlers - INFO - Message has video: False
2025-07-22 10:36:48,774 - handlers.session_handlers - INFO - Message has document: False
2025-07-22 10:36:48,775 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-22 10:36:50,311 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:36:52,250 - handlers.callback_handlers - INFO - Processing callback query from user **********: joined
2025-07-22 10:36:54,203 - handlers.callback_handlers - INFO - Processing callback query from user **********: setAccountInfo
2025-07-22 10:36:58,078 - handlers.callback_handlers - INFO - Processing callback query from user **********: set account_number
2025-07-22 10:37:16,687 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-22 10:37:16,687 - __main__ - INFO - Shutting down bot...
2025-07-22 10:37:17,348 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-22 10:37:17,401 - config.database - INFO - Disconnected from MongoDB
2025-07-22 10:37:17,401 - __main__ - INFO - Bot shutdown completed
2025-07-22 10:37:17,784 - __main__ - INFO - Received signal 2, initiating shutdown...
