2025-07-21 16:26:16,262 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-21 16:26:18,343 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:18,344 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:21,106 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:23,235 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,173 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:26,174 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:27,077 - services.user_service - INFO - Created new user: ********** (Getha)
2025-07-21 16:26:28,478 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:26:30,267 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Getha (ID: **********): success
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:32,509 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:44,630 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:48,802 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-21 16:26:59,397 - services.user_service - INFO - Created new user: 8135061794 (Someshhh)
2025-07-21 16:27:00,564 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:01,883 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Someshhh (ID: 8135061794): success
2025-07-21 16:27:12,254 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Getha
2025-07-21 16:27:39,740 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Someshhh
2025-07-21 16:27:40,568 - services.user_service - INFO - Created new user: ********** (Goat)
2025-07-21 16:27:41,848 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:27:43,426 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Goat (ID: **********): success
2025-07-21 16:28:42,357 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-21 16:28:45,850 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687e1d517104627ee2037073'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has text: True
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has photo: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has video: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message has document: False
2025-07-21 16:28:45,851 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-21 16:28:48,852 - services.promotion_report_service - INFO - User ********** has 274 referrals, using optimized calculation
2025-07-21 16:28:54,619 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:01,835 - handlers.callback_handlers - ERROR - Error in _handle_redeem_gift_code: Message to edit not found
2025-07-21 16:29:03,431 - services.user_service - INFO - Created new user: 1963388963 (Sudip)
2025-07-21 16:29:04,582 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-21 16:29:05,865 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sudip (ID: 1963388963): success
2025-07-21 16:29:19,356 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753095559.196fdefa
2025-07-21 16:29:25,699 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-21 16:29:29,919 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Sudip
2025-07-21 16:29:55,103 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-21 16:29:55,104 - __main__ - INFO - Shutting down bot...
2025-07-21 16:29:55,791 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-21 16:29:55,835 - config.database - INFO - Disconnected from MongoDB
2025-07-21 16:29:55,836 - __main__ - INFO - Bot shutdown completed
