#!/usr/bin/env python3
"""
Test script to verify broadcast service handles reply_markup correctly
"""

import asyncio
import sys
import os

# Add the python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python'))

from services.broadcast_service import BroadcastService
from config.settings import settings

async def test_broadcast_service_reply_markup():
    """Test broadcast service handling of reply_markup"""
    print("🧪 Testing Broadcast Service Reply Markup Handling...")

    # Import telegram classes
    from telegram import Bot, InlineKeyboardMarkup, InlineKeyboardButton

    # Create test keyboard
    test_keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton('🎁 Claim Bonus', callback_data='verify_gift_broadcast_test123')]
    ])
    
    # Create test broadcast data with reply_markup
    test_broadcast_data = {
        'type': 'gift',
        'text': '🎁 <b>EXTRA BONUS</b> 👇!\n\n👉 <a href="https://t.me/testchannel"><b>Click & Join Channel</b></a>\n\n<blockquote>👆 <b>Must Join Above Channel</b> \n<b>Before Click [🎁 Claim Bonus]</b></blockquote>\n\n👇👇👇👇👇',
        'reply_markup': test_keyboard.to_dict(),
        'gift_data': {
            'broadcast_id': 'test123',
            'channel_title': 'Test Channel',
            'reward_amount': 50.0
        }
    }
    
    print("\n📝 Test broadcast data structure:")
    print(f"- Type: {test_broadcast_data['type']}")
    print(f"- Has reply_markup: {'reply_markup' in test_broadcast_data}")
    print(f"- Reply markup type: {type(test_broadcast_data['reply_markup'])}")
    print(f"- Reply markup content: {test_broadcast_data['reply_markup']}")
    
    # Initialize broadcast service
    broadcast_service = BroadcastService()
    bot = Bot(settings.BOT_TOKEN)
    broadcast_service.set_bot(bot)
    
    # Test the reply_markup handling logic
    print("\n⌨️ Testing reply_markup handling logic...")
    
    # Simulate the logic from the broadcast service
    reply_markup = None
    if test_broadcast_data.get('buttons'):
        print("❌ Found 'buttons' field (should not be present for gift broadcasts)")
        reply_markup = broadcast_service._create_inline_keyboard(test_broadcast_data['buttons'])
    elif test_broadcast_data.get('reply_markup'):
        print("✅ Found 'reply_markup' field (correct for gift broadcasts)")
        # Handle pre-built reply_markup (for gift broadcasts)
        if isinstance(test_broadcast_data['reply_markup'], dict):
            print("✅ Reply markup is dict, converting to InlineKeyboardMarkup")
            reply_markup = InlineKeyboardMarkup.de_json(test_broadcast_data['reply_markup'], None)
        else:
            print("✅ Reply markup is already InlineKeyboardMarkup object")
            reply_markup = test_broadcast_data['reply_markup']
    else:
        print("❌ No keyboard data found")
    
    if reply_markup:
        print(f"✅ Successfully created reply_markup: {reply_markup}")
        print(f"✅ Button text: '{reply_markup.inline_keyboard[0][0].text}'")
        print(f"✅ Button callback: '{reply_markup.inline_keyboard[0][0].callback_data}'")
    else:
        print("❌ Failed to create reply_markup")
    
    print("\n🎯 Testing message sending simulation...")
    
    # Simulate what would happen during broadcast
    message_text = test_broadcast_data.get('text', '')
    print(f"Message text length: {len(message_text)} characters")
    print(f"Has reply_markup for sending: {reply_markup is not None}")
    
    if reply_markup:
        print("✅ Message would be sent with keyboard buttons")
        print(f"✅ Number of button rows: {len(reply_markup.inline_keyboard)}")
        print(f"✅ Number of buttons in first row: {len(reply_markup.inline_keyboard[0])}")
    else:
        print("❌ Message would be sent WITHOUT keyboard buttons")
    
    print("\n✅ Broadcast service reply_markup test completed!")
    return reply_markup is not None

async def main():
    """Main test function"""
    print("🚀 Starting Broadcast Service Reply Markup Test...")
    
    try:
        success = await test_broadcast_service_reply_markup()
        
        if success:
            print("\n🎉 Test passed! Broadcast service should handle gift broadcast buttons correctly.")
        else:
            print("\n❌ Test failed! Broadcast service is not handling reply_markup correctly.")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(main())
